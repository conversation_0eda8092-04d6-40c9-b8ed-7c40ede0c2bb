#include "led_control.h"

namespace LEDControl {

void initializeLeds() {
    // Initialize RGB LED pins as outputs
    pinMode(LED_RED, OUTPUT);
    pinMode(LED_GREEN, OUTPUT);
    pinMode(LED_ON, OUTPUT);
    pinMode(INSIDE_LED, OUTPUT);
    pinMode(OUTSIDE_LED, OUTPUT);

    // Set initial states
    digitalWrite(LED_RED, LOW);     // Turn off red LED
    digitalWrite(LED_GREEN, LOW);   // Turn off green LED
    digitalWrite(LED_ON, HIGH);     // Turn on level shifter
    digitalWrite(INSIDE_LED, LOW);  // Turn off inside LED
    digitalWrite(OUTSI<PERSON>_LED, LOW); // Turn off outside LED
}

void setLedColor(Color color) {
    switch (color) {
        case Color::OFF:
            digitalWrite(LED_RED, LOW);
            digitalWrite(LED_GREEN, LOW);
            break;
        case Color::RED:
            digitalWrite(LED_RED, HIGH);
            digitalWrite(LED_GREEN, LOW);
            break;
        case Color::GREEN:
            digitalWrite(LED_RED, LOW);
            digitalWrite(LED_GRE<PERSON>, HIGH);
            break;
        case Color::YELLOW:
            digitalWrite(LED_RED, HIGH);
            digitalWrite(LED_GREEN, HIGH);
            break;
    }
}

void updateWaitingLed() {
    if (cupState == CupState::WAITING_FOR_CUP) {
        uint32_t currentTime = millis();

        // Check if it's time to toggle the LED
        if (currentTime - lastWaitingLedUpdate >= WAITING_LED_FLASH_INTERVAL) {
            lastWaitingLedUpdate = currentTime;
            waitingLedState = !waitingLedState;  // Toggle state

            if (waitingLedState) {
                // Turn on green LED
                digitalWrite(LED_RED, LOW);
                digitalWrite(LED_GREEN, HIGH);
            } else {
                // Turn off both LEDs
                digitalWrite(LED_RED, LOW);
                digitalWrite(LED_GREEN, LOW);
            }
        }
    }
}

void turnOffRgbLeds() {
    setLedColor(Color::OFF);
}

void setRedLed() {
    setLedColor(Color::RED);
}

void setGreenLed() {
    setLedColor(Color::GREEN);
}

void setYellowLed() {
    setLedColor(Color::YELLOW);
}

void resetWaitingLedState() {
    lastWaitingLedUpdate = 0;
    waitingLedState = false;
}

void outsideLight() {
    uint32_t currentTime = millis();

    // Update accumulator every second
    if (currentTime - lastOutsideLightUpdate >= OUTSIDE_LIGHT_UPDATE_INTERVAL) {
        lastOutsideLightUpdate = currentTime;

        // Read light sensor (INPUT_3: 0 = dark, 1 = bright)
        if (digitalRead(INPUT_3) == 0) {
            // Dark detected - increment accumulator (but don't exceed max range)
            if (outsideLightAccumulator < OUTSIDE_LIGHT_MAX_RANGE) {
                outsideLightAccumulator++;
            }
        } else {
            // Bright detected - decrement accumulator (but don't go below negative max range)
            if (outsideLightAccumulator > -OUTSIDE_LIGHT_MAX_RANGE) {
                outsideLightAccumulator--;
            }
        }
    }

    // Control outside light based on accumulator value
    if (outsideLightAccumulator > 10) {
        digitalWrite(OUTSIDE_LED, HIGH);  // Turn on light when accumulator is positive
    }
    else if(outsideLightAccumulator < -10) {
        digitalWrite(OUTSIDE_LED, LOW);   // Turn off light when accumulator is negative
    }
}

void turnOnInsideLed() {
    digitalWrite(INSIDE_LED, HIGH);
}

void turnOffInsideLed() {
    digitalWrite(INSIDE_LED, LOW);
}

void turnOnOutsideLed() {
    digitalWrite(OUTSIDE_LED, HIGH);
}

void turnOffOutsideLed() {
    digitalWrite(OUTSIDE_LED, LOW);
}

} // namespace LEDControl
