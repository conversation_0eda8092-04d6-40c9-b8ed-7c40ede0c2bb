#include "motor_control.h"
#include "../types/cup_types.h"
#include "../utils/debug.h"
#include "../config/global_variables.h"
#include <Arduino.h>

// External references to global variables and objects
extern MCP23017 mcp;

namespace MotorControl {

// Motor control variables
HardwareTimer *stepTimer = nullptr;
volatile bool stepEnabled = false;

void initializeMotorControl() {
    // Initialize step pin
    pinMode(STEP_PIN, OUTPUT);
    digitalWrite(STEP_PIN, LOW);
    
    // Initialize precise timer for motor control (normal speed)
    stepTimer = new HardwareTimer(TIM2);
    stepTimer->setOverflow(NORMAL_SPEED, MICROSEC_FORMAT); // Start with normal speed
    stepTimer->attachInterrupt(stepTimerCallback);
    stepTimer->pause(); // Start paused
    
    DEBUG_PRINTLN("STM32 Timer initialized for variable speed motor control");
}

// Function to set motor speed
void setMotorSpeed(uint32_t speedMicros) {
    if (stepTimer != nullptr) {
        stepTimer->pause();
        stepTimer->setOverflow(speed<PERSON><PERSON><PERSON>, MICROSEC_FORMAT);
        if (stepEnabled) {
            stepTimer->resume();
        }
    }
}

// Timer callback for precise motor steps (variable speed)
void stepTimerCallback() {
    if (stepEnabled) {
        digitalWrite(STEP_PIN, HIGH);
        delayMicroseconds(4);               // 5 µs pulse width
        digitalWrite(STEP_PIN, LOW);

        // Count steps for final movement (both return paths)
        if (cupState == CupState::MOVING_TO_START || cupState == CupState::RETURNING_TO_CUSTOMER) {
            finalStepCount++;
        }

        // Count steps for error detection during MOVING_TO_SCANNING_POINT
        if (cupState == CupState::MOVING_TO_SCANNING_POINT && startStepCounting) {
            errorStepCount++;
        }

        // Count steps for error recovery
        if (cupState == CupState::ERROR_RECOVERY) {
            errorStepCount++;
        }
    }
}

// Motor control functions
void enable_motor() {
    mcp.writeRegister(MCP23017Register::GPIO_A, mcp.readRegister(MCP23017Register::GPIO_A) | (1 << PIN_EN));    // set EN pin high
}

void disable_motor() {
    mcp.writeRegister(MCP23017Register::GPIO_A, mcp.readRegister(MCP23017Register::GPIO_A) & ~(1 << PIN_EN)); // set EN pin low
}

void set_direction(Direction direction) {
    if (direction == Direction::LEFT) {
        mcp.writeRegister(MCP23017Register::GPIO_A, mcp.readRegister(MCP23017Register::GPIO_A) & ~(1 << PIN_DIR)); // set DIR pin low
    } else {
        mcp.writeRegister(MCP23017Register::GPIO_A, mcp.readRegister(MCP23017Register::GPIO_A) | (1 << PIN_DIR)); // set DIR pin high
    }
}

} // namespace MotorControl
