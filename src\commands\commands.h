#pragma once

#include <Arduino.h>

// Command definitions
enum Command {
    CMD_READ_INPUT = 0,
    CMD_UNLOCK_DOOR = 1,
    CMD_READ_TEMPERATURE = 2,
    CMD_MOTOR_MOVE = 4,
    CMD_CUP_OK = 5,
    <PERSON><PERSON>_CUP_NOT_OK = 6,
    CMD_ERROR_PROCESSED = 7,
    CMD_PAYMENT_PROCESSED = 8,
    CMD_JUMP_TO_BOOTLOADER = 9
};

// Base command handler interface
class CommandHandler {
public:
    virtual ~CommandHandler() = default;
    virtual void handleCommand(uint8_t sensorId, const uint8_t* data, uint8_t length) = 0;
protected:
    // Utility function to send response that all handlers will need
    void sendResponse(uint8_t sensorId, const uint8_t* data, uint8_t dataLen);
}; 