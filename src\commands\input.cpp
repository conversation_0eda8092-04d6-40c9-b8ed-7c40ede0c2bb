#include "input.h"
#include "../utils/debug.h"
#include "../config/pinout.h"

void InputCommandHandler::handleCommand(uint8_t sensorId, const uint8_t* data, uint8_t length) {
    DEBUG_PRINT("Reading input ");
    Serial2.println(sensorId);

    if (sensorId > 12) {  // 6 normal + 6 NPN inputs
        DEBUG_PRINTLN("Invalid input ID!");
        uint8_t errorData[] = {0};  // Error response
        sendResponse(sensorId, errorData, 1);
        return;
    }

    bool state = readInput(sensorId);
    uint8_t response = state ? 1 : 0;
    
    DEBUG_PRINT("Input state: ");
    Serial2.println(state);
    
    sendResponse(sensorId, &response, 1);
}

bool InputCommandHandler::readInput(uint8_t inputId) {
    uint8_t pin;

    switch(inputId) {
        // Normal inputs (1-6)
        case 1:
            pin = Pinout::Inputs::Current::IN_1;
            break;
        case 2:
            pin = Pinout::Inputs::Current::IN_2;
            break;
        case 3:
            pin = Pinout::Inputs::Current::IN_3;
            break;
        case 4:
            pin = Pinout::Inputs::Current::IN_4;
            break;
        case 5:
            pin = Pinout::Inputs::Current::IN_5;
            break;
        case 6:
            pin = Pinout::Inputs::Current::IN_6;
            break;
        
        // NPN inputs (7-12)
        case 7:
            pin = Pinout::Inputs_NPN::Current::IN_1;
            break;
        case 8:
            pin = Pinout::Inputs_NPN::Current::IN_2;
            break;
        case 9:
            pin = Pinout::Inputs_NPN::Current::IN_3;
            break;
        case 10:
            pin = Pinout::Inputs_NPN::Current::IN_4;
            break;
        case 11:
            pin = Pinout::Inputs_NPN::Current::IN_5;
            break;
        case 12:
            pin = Pinout::Inputs_NPN::Current::IN_6;
            break;
        default:
            return false;
    }

    return digitalRead(pin);  // Čteme přímo bez inverze pro všechny vstupy
} 