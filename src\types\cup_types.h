#ifndef CUP_TYPES_H
#define CUP_TYPES_H

// All states of cup
enum class CupState {
    WAITING_FOR_CUP,             // "01": "waiting for cup",
    MOVING_TO_SCANNING_POINT,    // "02": "moving to scanning point",
    SCANNING,                    // "03": "scanning",
    MOVING_TO_BIN,               // "04": "moving to bin",
    UNLOADING_AT_BIN,            // "05": "waiting at bin till the cup falls",
    WAITING_FOR_PAYMENT,         // "06": "waiting for payment at bin",
    MOVING_TO_START,             // "07": "returning to start point",
    MOVING_TO_REVERSE_POINT,     // "08": "moving to reverse point, from which it will returns to customer",
    RETURNING_TO_CUSTOMER,       // "09": "returning cup to customer",
    ERROR_RECOVERY,              // "0A": "error recovery - moving back to start",
    ERROR,                       // "0B": "error state - motor enabled but stopped",
};

// Color of LEDs
enum class Color {
    OFF,
    RED,
    GREEN,
    Y<PERSON><PERSON><PERSON>,
};

#endif // CUP_TYPES_H
