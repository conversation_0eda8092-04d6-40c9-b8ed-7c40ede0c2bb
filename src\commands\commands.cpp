#include "commands.h"
#include "../utils/debug.h"
#include "../communication/protocol.h"
#include "../config/global_variables.h"

// External declarations for buffers
extern HardwareSerial Serial1;

void CommandHandler::sendResponse(uint8_t sensorId, const uint8_t* data, uint8_t dataLen) {
    DEBUG_PRINTLN("\n=== Preparing Response ===");
    DEBUG_PRINT("Sensor ID: ");
    Serial2.println(sensorId);
    DEBUG_PRINT("Input data length: ");
    Serial2.println(dataLen);

    // Build response packet
    DEBUG_PRINTLN("Building response packet...");
    uint8_t idx = 0;
    txBuffer[idx++] = START_BYTE;  // START
    txBuffer[idx++] = RESP_OK;     // Always send 1 for success
    
    // Calculate actual data length (status + sensorId + data)
    uint8_t totalDataLen = 1 + 1 + dataLen;  // 1 for status + 1 for sensorId + actual data length
    txBuffer[idx++] = totalDataLen;      // LENGTH
    
    DEBUG_PRINT("Total data section length: ");
    Serial2.println(totalDataLen);
    
    // Add status, sensorId and data
    txBuffer[idx++] = RESP_OK;     // Status code 1 = OK
    txBuffer[idx++] = sensorId;    // Sensor ID
    
    if (data && dataLen > 0) {
        DEBUG_PRINTLN("Copying data bytes:");
        for(uint8_t i = 0; i < dataLen; i++) {
            txBuffer[idx] = data[i];
            DEBUG_PRINT("  Byte ");
            Serial2.print(i);
            DEBUG_PRINT(": 0x");
            DEBUG_PRINT_HEX(data[i]);
            Serial2.println();
            idx++;
        }
    }

    // Add CRC and END byte
    DEBUG_PRINTLN("Calculating CRC...");
    uint16_t crc = calculateCRC16(txBuffer, idx);
    DEBUG_PRINT("CRC calculated: 0x");
    DEBUG_PRINT_HEX((uint8_t)(crc >> 8));
    DEBUG_PRINT_HEX((uint8_t)(crc & 0xFF));
    Serial2.println();
    
    txBuffer[idx++] = (crc >> 8) & 0xFF;
    txBuffer[idx++] = crc & 0xFF;
    txBuffer[idx++] = END_BYTE;

    DEBUG_PRINTLN("\nFinal packet structure:");
    DEBUG_PACKET("Complete response packet", txBuffer, idx);
    DEBUG_PRINTLN("Sending response...");

    // Clear any pending data first
    while(Serial1.available()) Serial1.read();

    // Small delay to ensure line is clear
    delay(5);

    // Send complete response at once for better timing
    Serial1.flush();
    size_t written = Serial1.write(txBuffer, idx);
    Serial1.flush();  // Ensure all data is sent

    // Small delay after sending to ensure data is processed
    delay(5);

    Serial1.flush();
    
    DEBUG_PRINT("Bytes written: ");
    Serial2.println(written);
    
    if (written != idx) {
        DEBUG_PRINTLN("WARNING: Not all bytes were written!");
        DEBUG_PRINT("Expected: ");
        Serial2.print(idx);
        DEBUG_PRINT(" Got: ");
        Serial2.println(written);
    } else {
        DEBUG_PRINTLN("Response sent successfully!");
    }
    DEBUG_PRINTLN("=== End Response ===\n");
} 