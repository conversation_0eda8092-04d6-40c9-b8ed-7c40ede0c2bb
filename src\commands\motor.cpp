#include "motor.h"
#include "../config/pinout.h"
#include <Arduino.h>
#include <MCP23017.h>

// External MCP23017 instance declaration
extern MCP23017 mcp;
extern HardwareSerial Serial2;

void MotorCommandHandler::handleCommand(uint8_t sensorId, const uint8_t* data, uint8_t length) {
    // Check if we have enough data (2 bytes for steps, 1 byte for direction)
    if (length < 3) {
        DEBUG_PRINTLN("Motor command: Invalid data length");
        return;
    }

    // Extract parameters from data
    uint16_t steps = (data[0] << 8) | data[1];  // Combine two bytes into steps
    bool direction = data[2] != 0;               // Convert byte to boolean

    DEBUG_PRINT("Motor command: Steps=");
    Serial2.print(steps);
    DEBUG_PRINT(" Direction=");
    DEBUG_PRINTLN(direction ? "CW" : "CCW");

    // Execute the movement
    moveMotor(steps, direction);

    // Send success response
    uint8_t response = 0x00;  // Success status
    sendResponse(sensorId, &response, 1);
}

void MotorCommandHandler::setMotorDirection(bool direction) {
    // Read current GPIO state
    uint8_t currentState = mcp.readRegister(MCP23017Register::GPIO_A);
    
    // Update DIR bit (GPA6) while preserving other bits
    if (direction) {
        currentState |= (1 << 6);   // Set bit 6 (DIR) to 1
    } else {
        currentState &= ~(1 << 6);  // Set bit 6 (DIR) to 0
    }
    
    mcp.writeRegister(MCP23017Register::GPIO_A, currentState);
}

void MotorCommandHandler::setMotorEnabled(bool enabled) {
    // Read current GPIO state
    uint8_t currentState = mcp.readRegister(MCP23017Register::GPIO_A);
    
    // Update EN bit (GPA5) while preserving other bits
    if (enabled) {
        currentState |= (1 << 5);   // Set bit 5 (EN) to 1
    } else {
        currentState &= ~(1 << 5);  // Set bit 5 (EN) to 0
    }
    
    mcp.writeRegister(MCP23017Register::GPIO_A, currentState);
}

void MotorCommandHandler::moveMotor(uint16_t steps, bool direction) {
    // Set direction first
    setMotorDirection(direction);
    
    // Enable motor
    setMotorEnabled(true);
    
    // Execute steps
    for (uint16_t i = 0; i < steps; i++) {
        digitalWrite(Pinout::Motor::Current::STEP_PIN, HIGH);
        delayMicroseconds(100);   // Adjust this delay for speed control
        digitalWrite(Pinout::Motor::Current::STEP_PIN, LOW);
        delayMicroseconds(100);   // Adjust this delay for speed control
    }
    
    // Disable motor
    setMotorEnabled(false);
} 