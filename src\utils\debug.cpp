#include "debug.h"

void DEBUG_PRINT(const char* msg) {
    if (DEBUG_ENABLED) {
        Serial2.print(msg);
    }
}

void DEBUG_PRINTLN(const char* msg) {
    if (DEBUG_ENABLED) {
        Serial2.println(msg);
    }
}

void DEBUG_PRINT_HEX(uint8_t value) {
    if (DEBUG_ENABLED) {
        if (value < 0x10) Serial2.print("0");
        Serial2.print(value, HEX);
    }
}

void DEBUG_PACKET(const char* prefix, const uint8_t* data, size_t length) {
    if (PACKET_TRACE) {
        Serial2.print(prefix);
        Serial2.print(" [");
        Serial2.print(length);
        Serial2.print(" bytes]: ");
        for (size_t i = 0; i < length; i++) {
            DEBUG_PRINT_HEX(data[i]);
            Serial2.print(" ");
        }
        Serial2.println();
    }
} 