; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:nucleo_l552ze_q]
platform = ststm32				
board = nucleo_l552ze_q
framework = arduino
build_flags = -DCUSTOM_SYSTEM_CLOCK -DHAL_INCLUDE -DUSE_HAL_DRIVER
upload_protocol = stlink
monitor_port = COM5
monitor_speed = 115200
lib_deps =
	Wire
	blemasle/MCP23017@^2.0.0
	khoih-prog/FlashStorage_STM32@^1.2.0


#define PIN_DIAG0  1
#define PIN_DIAG1  2 
#define PIN_DC0    3 
#define PIN_EN     5 
#define PIN_DIR    6 