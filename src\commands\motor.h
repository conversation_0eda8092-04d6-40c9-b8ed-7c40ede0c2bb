#ifndef MOTOR_COMMANDS_H
#define MOTOR_COMMANDS_H

#include "commands.h"
#include "../utils/debug.h"

class MotorCommandHandler : public CommandHandler {
public:
    MotorCommandHandler() {}
    virtual void handleCommand(uint8_t sensorId, const uint8_t* data, uint8_t length) override;
private:
    void setMotorDirection(bool direction);
    void setMotorEnabled(bool enabled);
    void moveMotor(uint16_t steps, bool direction);
};

#endif // MOTOR_COMMANDS_H 