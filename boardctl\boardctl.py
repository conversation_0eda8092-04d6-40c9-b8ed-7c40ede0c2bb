#!/usr/bin/env python3
import argparse
import sys
import enum
import serial
import time
from typing import Optional, List, Tuple, Any

# py boardctl.py -d COM5 -a payment_processed

# Debug configuration
DEBUG_ENABLED = True  # Default debug state

# Protocol constants
START_BYTE = 0xAA
END_BYTE = 0x55
BOARD_SUCCESS = 0x01  # Board always sends 1 for success

# Return codes
# Success codes (positive)
RC_SUCCESS = 1        # Everything OK, board responded with success

# Error codes (zero or negative)
RC_ERROR = 0          # Generic error during processing
RC_TIMEOUT = -22      # Communication timeout
RC_INVALID_CRC = -2   # CRC check failed
RC_INVALID_PACKET = -3 # Invalid packet format
RC_INVALID_RESPONSE = -4 # Invalid response from board
RC_NO_CONNECTION = -13 # Could not connect to board

# Command definitions
class Command(enum.Enum):
    read_input = 0
    unlock_door = 1
    read_temperature = 2
    cup_ok = 5
    cup_not_ok = 6
    error_processed = 7
    payment_processed = 8
    jump_to_bootloader = 9

# Action definitions
INPUT = "input"
CHANNEL = "channel"
DOOR_ID = "door_id"     # New constant for door identification
O_STATE = "state"
O_TIME = "time"
O_ID = "id"
O_TEMP = "temperature"

ACTIONS = {
    "read_input":        (Command.read_input,          [INPUT],         [None, O_STATE]),
    "read_input_npn":    (Command.read_input,          [INPUT],         [None, O_STATE]),
    "unlock_service":    (Command.unlock_door,         [0],             []),  # Service door is ID 0
    "unlock_bin":        (Command.unlock_door,         [1],             []),  # Bin door is ID 1
    "read_temperature":  (Command.read_temperature,    [CHANNEL],       [O_ID, O_TEMP]),
    "cup_ok":            (Command.cup_ok,              [],              []),  # CUP OK command
    "cup_not_ok":        (Command.cup_not_ok,          [],              []),  # CUP NOT OK command
    "error_processed":   (Command.error_processed,     [],              []),  # ERROR PROCESSED command
    "payment_processed": (Command.payment_processed,   [],              []),  # PAYMENT PROCESSED command
    "bootloader":        (Command.jump_to_bootloader,  [],              []),  # Jump to bootloader command
}

def debug_print(msg: str) -> None:
    if DEBUG_ENABLED:
        print(msg)

def debug_packet(prefix: str, data: bytes) -> None:
    if DEBUG_ENABLED:
        hex_data = ' '.join(f'{b:02X}' for b in data)
        print(f"{prefix} [{len(data)} bytes]: {hex_data}")

class PacketBuilder:
    @staticmethod
    def build_command(cmd: Command, params: List[Any]) -> bytes:
        """Build a command packet.
        
        Format: [START][CMD][LENGTH][PARAMS][CRC16][END]
        LENGTH is the length of PARAMS + CRC(2) + END(1)
        """
        debug_print("\n=== Building Command Packet ===")
        debug_print(f"Command: {cmd.name} (0x{cmd.value:02X})")
        debug_print(f"Parameters: {params}")
        
        # Convert parameters to bytes
        params_bytes = b''
        for param in params:
            if isinstance(param, bool):
                params_bytes += bytes([1 if param else 0])
            elif isinstance(param, int):
                params_bytes += bytes([param])
            else:
                raise ValueError(f"Unsupported parameter type: {type(param)}")
        
        # Calculate total length (params + CRC + END)
        total_length = len(params_bytes) + 3
        
        # Build initial packet
        packet = bytes([
            START_BYTE,
            cmd.value,
            total_length,
        ]) + params_bytes
        
        # Calculate and add CRC
        crc = calculate_crc16(packet)
        packet += bytes([
            (crc >> 8) & 0xFF,
            crc & 0xFF,
            END_BYTE
        ])
        
        debug_packet("Command packet", packet)
        debug_print("=== End Building Packet ===\n")
        return packet

    @staticmethod
    def parse_response(data: bytes) -> Tuple[int, List[Any]]:
        """
        Format: [START][RESP_OK][LENGTH][STATUS][SENSOR_ID][DATA][CRC16][END]
        """
        try:
            debug_print("\n=== Parsing Response ===")
            debug_packet("Response data", data)
            
            # Basic validation
            if len(data) < 7:  # START + RESP_OK + LENGTH + STATUS + SENSOR_ID + CRC(2) + END
                debug_print("Error: Response too short")
                return RC_ERROR, []
                
            if data[0] != START_BYTE:
                debug_print(f"Error: Invalid start byte: 0x{data[0]:02X}")
                return RC_ERROR, []
                
            if data[-1] != END_BYTE:
                debug_print(f"Error: Invalid end byte: 0x{data[-1]:02X}")
                return RC_ERROR, []
            
            # Extract fields
            board_resp = data[1]
            data_length = data[2]  # Length of DATA section (STATUS + SENSOR_ID + DATA)
            total_expected = 1 + 1 + 1 + data_length + 2 + 1  # START + RESP_OK + LENGTH + DATA + CRC + END
            
            debug_print(f"Board response: 0x{board_resp:02X}")
            debug_print(f"Data length field: {data_length}")
            debug_print(f"Total expected length: {total_expected}")
            debug_print(f"Actual packet length: {len(data)}")
            
            if len(data) != total_expected:
                debug_print("Error: Invalid packet length")
                return RC_ERROR, []
            
            # Verify board response and status
            if board_resp != BOARD_SUCCESS or data[3] != BOARD_SUCCESS:
                debug_print("Error: Board indicated failure")
                return RC_ERROR, []
            
            # Extract data (skip STATUS byte)
            data_section = data[4:3+data_length]  # From SENSOR_ID to end of DATA
            debug_print("Data section: " + " ".join(f"0x{b:02X}" for b in data_section))
            
            # Verify CRC
            received_crc = (data[-3] << 8) | data[-2]
            calculated_crc = calculate_crc16(data[:-3])
            
            debug_print(f"Received CRC: 0x{received_crc:04X}")
            debug_print(f"Calculated CRC: 0x{calculated_crc:04X}")
            
            if received_crc != calculated_crc:
                debug_print("Error: CRC mismatch")
                return RC_INVALID_CRC, []
            
            debug_print("=== End Parsing ===\n")
            return RC_SUCCESS, list(data_section)
            
        except Exception as e:
            debug_print(f"Error parsing response: {e}")
            return RC_ERROR, []

class Board:
    def __init__(self, device: str, baudrate: int = 115200, timeout: float = 1.0):
        debug_print(f"\n=== Initializing Board ===")
        debug_print(f"Device: {device}")
        debug_print(f"Baudrate: {baudrate}")
        debug_print(f"Timeout: {timeout}s")
        
        try:
            self.serial = serial.Serial(
                port=device,
                baudrate=baudrate,
                timeout=timeout,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE
            )
            
            debug_print("Waiting for board initialization...")
            time.sleep(2)
            
            # Clear buffers
            self.serial.reset_input_buffer()
            self.serial.reset_output_buffer()
            
            while self.serial.in_waiting:
                data = self.serial.read(self.serial.in_waiting)
                debug_packet("Cleared buffer data", data)
                time.sleep(0.1)
            
            debug_print("=== Initialization Complete ===\n")
            
        except serial.SerialException as e:
            debug_print(f"Failed to connect to board: {e}")
            raise

    def execute_command(self, action: str, params: List[Any]) -> Tuple[int, List[Any]]:
        """Execute a command and return (return_code, data).
        
        return_code will be:
        - RC_SUCCESS (1) if command was successful
        - RC_ERROR (0) if there was an error processing the command
        - RC_NO_CONNECTION (13) if we couldn't connect to the board
        """
        try:
            debug_print(f"\n=== Executing Command: {action} ===")
            debug_print(f"Parameters: {params}")
            
            if action not in ACTIONS:
                raise ValueError(f"Unknown action: {action}")
            
            cmd, required_params, expected_outputs = ACTIONS[action]
            
            # Special handling for unlock_service
            if action == "unlock_service":
                params = [0]  # Service door is ID 0
            elif action == "unlock_bin":
                params = [1]  # Bin door is ID 1
            # Special handling for cup commands
            elif action == "cup_ok":
                params = [0]  # CUP OK command with sensor ID 0
            elif action == "cup_not_ok":
                params = [0]  # CUP NOT OK command with sensor ID 0
            elif action == "error_processed":
                params = [0]  # ERROR PROCESSED command with sensor ID 0
            elif action == "bootloader":
                params = [0]  # Jump to bootloader command with sensor ID 0
            # Special handling for NPN inputs
            elif action == "read_input_npn":
                if len(params) != 1:
                    raise ValueError("read_input_npn requires exactly one parameter")
                params = [params[0] + 6]  # Přičteme 6 k číslu vstupu
            elif len(params) != len(required_params):
                raise ValueError(f"Action {action} requires {len(required_params)} parameters, got {len(params)}")
            
            # Build and send packet
            packet = PacketBuilder.build_command(cmd, params)
            debug_print("\nSending packet...")

            # Clear input buffer multiple times to handle continuous data
            self.serial.reset_input_buffer()
            time.sleep(0.01)  # Wait a bit
            self.serial.reset_input_buffer()  # Clear again

            # Send complete packet at once for better timing
            self.serial.write(packet)
            self.serial.flush()

            # Wait a bit for the board to process and respond
            time.sleep(0.02)

            debug_print("Packet sent, waiting for response...")
            
            # Wait for response
            response = bytearray()
            timeout_time = time.time() + self.serial.timeout
            start_found = False
            data_length = None
            total_expected = None
            
            while time.time() < timeout_time:
                if self.serial.in_waiting:
                    byte = self.serial.read(1)
                    if not byte:
                        continue
                    
                    debug_print(f"Received byte: 0x{byte[0]:02X}")
                    
                    # Look for start byte
                    if not start_found:
                        if byte[0] == START_BYTE:
                            start_found = True
                            response.clear()
                            response.extend(byte)
                            debug_print("Start byte found")
                        continue
                    
                    # Add byte to response
                    response.extend(byte)
                    debug_packet("Current buffer", response)
                    
                    # Once we have length byte, calculate expected total
                    if len(response) == 3 and data_length is None:
                        data_length = response[2]
                        total_expected = 1 + 1 + 1 + data_length + 2 + 1  # START + RESP_OK + LENGTH + DATA + CRC + END
                        debug_print(f"Data length field: {data_length}")
                        debug_print(f"Total expected length: {total_expected}")
                    
                    # Check if we have a complete packet
                    if total_expected and len(response) == total_expected:
                        debug_print("\n=== Complete Packet Received ===")
                        debug_packet("Final packet", response)
                        return PacketBuilder.parse_response(response)
                
                time.sleep(0.001)  # Small delay to prevent busy waiting
            
            debug_print("\n=== Timeout ===")
            if response:
                debug_packet("Incomplete response", response)
            else:
                debug_print("No data received")
            debug_print("=== End Timeout ===\n")
            
            return RC_TIMEOUT, []
            
        except serial.SerialException as e:
            debug_print(f"Serial communication error: {e}")
            return RC_NO_CONNECTION, []
        except Exception as e:
            debug_print(f"Error executing command: {e}")
            return RC_ERROR, []

    def close(self):
        if hasattr(self, 'serial') and self.serial.is_open:
            self.serial.close()
            debug_print("Serial port closed")

def create_parser():
    parser = argparse.ArgumentParser(description='Board Control CLI')
    parser.add_argument('-d', '--device', default='/dev/ttyUSB0',
                      help='Serial device (default: /dev/ttyUSB0)')
    parser.add_argument('-b', '--baudrate', type=int, default=115200,
                      help='Baudrate (default: 115200)')
    parser.add_argument('-a', '--action', required=True,
                      choices=list(ACTIONS.keys()),
                      help='Action to perform')
    parser.add_argument('params', nargs='*',
                      help='Parameters for the action')
    parser.add_argument('--debug', action='store_true',
                      help='Enable debug output')
    return parser

def main():
    parser = create_parser()
    args = parser.parse_args()
    
    # Set debug state based on argument
    global DEBUG_ENABLED
    DEBUG_ENABLED = args.debug
    
    try:
        board = Board(args.device, args.baudrate)
        
        # Parse parameters
        params = []
        cmd_params = ACTIONS[args.action][1]
        
        for i, (param, param_type) in enumerate(zip(args.params, cmd_params)):
            if param_type in [INPUT, CHANNEL]:
                params.append(int(param))
            elif isinstance(param_type, bool):
                params.append(param.lower() == 'true')
            else:
                params.append(param)
        
        # Execute command
        return_code, response_data = board.execute_command(args.action, params)
        
        # Print response in format [return_code] [sensor_id] [value]
        if return_code == RC_SUCCESS and response_data:
            sensor_id = response_data[0] if len(response_data) > 0 else 0
            if args.action == "read_temperature" and len(response_data) >= 3:
                # Convert fixed point back to float (divide by 10)
                temp_fixed = (response_data[1] << 8) | response_data[2]
                temp = temp_fixed / 10.0
                print(f"{return_code} {sensor_id} {temp}")
            else:
                value = response_data[1] if len(response_data) > 1 else 0
                print(f"{return_code} {sensor_id} {value}")
        else:
            print(f"{return_code}")
            
        return return_code
        
    except Exception as e:
        if DEBUG_ENABLED:
            print(f"Error: {e}")
        return RC_ERROR
        
    finally:
        if 'board' in locals():
            board.close()
            if DEBUG_ENABLED:
                print("Serial port closed")

def calculate_crc16(data: bytes) -> int:
    crc = 0xFFFF
    for byte in data:
        crc = ((crc << 8) & 0xFF00) ^ crc16_table[(crc >> 8) ^ byte]
    return crc

# CRC-16-CCITT lookup table
crc16_table = [
    0x0000, 0x1021, 0x2042, 0x3063, 0x4084, 0x50A5, 0x60C6, 0x70E7,
    0x8108, 0x9129, 0xA14A, 0xB16B, 0xC18C, 0xD1AD, 0xE1CE, 0xF1EF,
    0x1231, 0x0210, 0x3273, 0x2252, 0x52B5, 0x4294, 0x72F7, 0x62D6,
    0x9339, 0x8318, 0xB37B, 0xA35A, 0xD3BD, 0xC39C, 0xF3FF, 0xE3DE,
    0x2462, 0x3443, 0x0420, 0x1401, 0x64E6, 0x74C7, 0x44A4, 0x5485,
    0xA56A, 0xB54B, 0x8528, 0x9509, 0xE5EE, 0xF5CF, 0xC5AC, 0xD58D,
    0x3653, 0x2672, 0x1611, 0x0630, 0x76D7, 0x66F6, 0x5695, 0x46B4,
    0xB75B, 0xA77A, 0x9719, 0x8738, 0xF7DF, 0xE7FE, 0xD79D, 0xC7BC,
    0x48C4, 0x58E5, 0x6886, 0x78A7, 0x0840, 0x1861, 0x2802, 0x3823,
    0xC9CC, 0xD9ED, 0xE98E, 0xF9AF, 0x8948, 0x9969, 0xA90A, 0xB92B,
    0x5AF5, 0x4AD4, 0x7AB7, 0x6A96, 0x1A71, 0x0A50, 0x3A33, 0x2A12,
    0xDBFD, 0xCBDC, 0xFBBF, 0xEB9E, 0x9B79, 0x8B58, 0xBB3B, 0xAB1A,
    0x6CA6, 0x7C87, 0x4CE4, 0x5CC5, 0x2C22, 0x3C03, 0x0C60, 0x1C41,
    0xEDAE, 0xFD8F, 0xCDEC, 0xDDCD, 0xAD2A, 0xBD0B, 0x8D68, 0x9D49,
    0x7E97, 0x6EB6, 0x5ED5, 0x4EF4, 0x3E13, 0x2E32, 0x1E51, 0x0E70,
    0xFF9F, 0xEFBE, 0xDFDD, 0xCFFC, 0xBF1B, 0xAF3A, 0x9F59, 0x8F78,
    0x9188, 0x81A9, 0xB1CA, 0xA1EB, 0xD10C, 0xC12D, 0xF14E, 0xE16F,
    0x1080, 0x00A1, 0x30C2, 0x20E3, 0x5004, 0x4025, 0x7046, 0x6067,
    0x83B9, 0x9398, 0xA3FB, 0xB3DA, 0xC33D, 0xD31C, 0xE37F, 0xF35E,
    0x02B1, 0x1290, 0x22F3, 0x32D2, 0x4235, 0x5214, 0x6277, 0x7256,
    0xB5EA, 0xA5CB, 0x95A8, 0x8589, 0xF56E, 0xE54F, 0xD52C, 0xC50D,
    0x34E2, 0x24C3, 0x14A0, 0x0481, 0x7466, 0x6447, 0x5424, 0x4405,
    0xA7DB, 0xB7FA, 0x8799, 0x97B8, 0xE75F, 0xF77E, 0xC71D, 0xD73C,
    0x26D3, 0x36F2, 0x0691, 0x16B0, 0x6657, 0x7676, 0x4615, 0x5634,
    0xD94C, 0xC96D, 0xF90E, 0xE92F, 0x99C8, 0x89E9, 0xB98A, 0xA9AB,
    0x5844, 0x4865, 0x7806, 0x6827, 0x18C0, 0x08E1, 0x3882, 0x28A3,
    0xCB7D, 0xDB5C, 0xEB3F, 0xFB1E, 0x8BF9, 0x9BD8, 0xABBB, 0xBB9A,
    0x4A75, 0x5A54, 0x6A37, 0x7A16, 0x0AF1, 0x1AD0, 0x2AB3, 0x3A92,
    0xFD2E, 0xED0F, 0xDD6C, 0xCD4D, 0xBDAA, 0xAD8B, 0x9DE8, 0x8DC9,
    0x7C26, 0x6C07, 0x5C64, 0x4C45, 0x3CA2, 0x2C83, 0x1CE0, 0x0CC1,
    0xEF1F, 0xFF3E, 0xCF5D, 0xDF7C, 0xAF9B, 0xBFBA, 0x8FD9, 0x9FF8,
    0x6E17, 0x7E36, 0x4E55, 0x5E74, 0x2E93, 0x3EB2, 0x0ED1, 0x1EF0
]

if __name__ == "__main__":
    sys.exit(main()) 