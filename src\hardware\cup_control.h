#ifndef CUP_CONTROL_H
#define CUP_CONTROL_H

#include <Arduino.h>
#include "../types/cup_types.h"
#include "../config/pinout.h"
#include "../config/global_variables.h"
#include "motor_control.h"
#include "led_control.h"
#include "../utils/debug.h"

// NPN input pins mapping
#define NPN_INPUT_1         Pinout::Inputs_NPN::Current::IN_1       // First inductive sensor, which detects, if cup was inserted
#define NPN_INPUT_2         Pinout::Inputs_NPN::Current::IN_2       // Second inductive sensor, which detects, if cup reached scanning point
#define NPN_INPUT_3         Pinout::Inputs_NPN::Current::IN_3       // Third inductive sensor, which detects, if cup is in reverse point to return cup to customer
#define NPN_INPUT_4         Pinout::Inputs_NPN::Current::IN_4       // Fourth inductive sensor, which detects, if cup reached bin point
#define NPN_INPUT_5         Pinout::Inputs_NPN::Current::IN_5       // Fifth end-switch at start point
#define NPN_INPUT_6         Pinout::Inputs_NPN::Current::IN_6       // Sixth end-switch at bin

// External variables that cup control needs access to
extern CupState cupState;
extern uint32_t cupProcessStartTime;
extern uint32_t binWaitStartTime;
extern uint32_t scanningStartTime;
extern uint32_t finalStepCount;
extern uint32_t errorStepCount;
extern uint32_t errorStopStartTime;
extern bool cupDecisionReceived;
extern bool cupOkDecision;
extern bool errorProcessedReceived;
extern bool paymentProcessedReceived;
extern uint32_t paymentWaitStartTime;
extern bool useSlowSpeed;
extern bool npnInput1WasHigh;
extern bool startStepCounting;

// External timing variables
extern uint32_t SCANNING_TIMEOUT;
extern uint32_t BIN_WAIT_TIME;
extern uint32_t FINAL_STEPS;
extern uint32_t MAX_STEPS_ERROR;
extern uint32_t ERROR_STOP_TIME;
extern uint32_t NORMAL_SPEED;
extern uint32_t SLOW_SPEED;
extern uint32_t PAYMENT_TIMEOUT;

// External cup state timeout variables
extern uint32_t MOVING_TO_SCANNING_POINT_TIMEOUT;
extern uint32_t MOVING_TO_BIN_TIMEOUT;
extern uint32_t MOVING_TO_START_TIMEOUT;
extern uint32_t MOVING_TO_REVERSE_POINT_TIMEOUT;
extern uint32_t RETURNING_TO_CUSTOMER_TIMEOUT;
extern uint32_t ERROR_RECOVERY_TIMEOUT;

namespace CupBoard {

/**
 * @brief Get cup state as string
 * @return String representation of the current cup state
 */
const char* getCupStateString();

/**
 * @brief Main cup control function
 * Handles the complete cup processing state machine including:
 * - Waiting for cup insertion
 * - Moving to scanning point
 * - Scanning process
 * - Moving to bin or reverse point based on decision
 * - Error recovery
 * - Returning cup to customer
 */
void cupProcess();

} // namespace CupBoard

#endif // CUP_CONTROL_H
