#ifndef GLOBAL_VARIABLES_H
#define GLOBAL_VARIABLES_H

#include <stdint.h>
#include <Arduino.h>
#include "../types/cup_types.h"
#include "../communication/protocol.h"

// EEPROM startup options
enum class EEPROMStartup {
    REWRITE,        // Write initial values to EEPROM
    LOAD_FROM_IT,   // Load initial values from EEPROM
    DO_NOTHING,     // Do nothing with EEPROM at startup
};

// EEPROM startup configuration
extern const EEPROMStartup eepromStartup;      // choose what to do with EEPROM at startup

// Cup process timing variables
extern uint32_t CUP_STATE_REPORT_INTERVAL;   // Send cup state every *** milliseconds
extern uint32_t BIN_WAIT_TIME;              // Wait *** microsecond at bin
extern uint32_t SCANNING_TIMEOUT;          // *** microseconds timeout for scanning (60000 ms)
extern uint32_t FINAL_STEPS;                  // *** steps after NPN_INPUT_1 turns off
extern uint32_t MAX_STEPS_ERROR;           // Maximum steps before error
extern uint32_t ERROR_STOP_TIME;            // *** microseconds stop during error recovery
extern uint32_t NORMAL_SPEED;               // *** microseconds (normal speed)
extern uint32_t SLOW_SPEED;                 // *** microseconds (slow speed)

// Cup state timeouts (in milliseconds)
extern uint32_t MOVING_TO_SCANNING_POINT_TIMEOUT;  // Timeout for moving to scanning point
extern uint32_t MOVING_TO_BIN_TIMEOUT;             // Timeout for moving to bin
extern uint32_t MOVING_TO_START_TIMEOUT;           // Timeout for moving to start
extern uint32_t MOVING_TO_REVERSE_POINT_TIMEOUT;   // Timeout for moving to reverse point
extern uint32_t RETURNING_TO_CUSTOMER_TIMEOUT;     // Timeout for returning to customer
extern uint32_t ERROR_RECOVERY_TIMEOUT;            // Timeout for error recovery movement

// Outside light variables
extern int32_t OUTSIDE_LIGHT_MAX_RANGE;      // Maximum range for light timer accumulator
extern uint32_t OUTSIDE_LIGHT_UPDATE_INTERVAL; // Update accumulator every *** microseconds
extern bool OUTSIDE_LIGHT_ENABLED;

// Inside LED variables
extern bool INSIDE_LED_ENABLED;

// RGB LED flashing variables for WAITING_FOR_CUP state
extern uint32_t WAITING_LED_FLASH_INTERVAL; // Flash interval in *** microseconds

// Payment timeout
extern uint32_t PAYMENT_TIMEOUT;           // Payment timeout in milliseconds (60 seconds)

// Security variables
extern bool SECURITY_STATE;                  // If true, doors status is sent to python script

// ===== GLOBAL VARIABLES MOVED FROM MAIN.CPP =====

// Communication buffer for responses
extern uint8_t txBuffer[MAX_PACKET_SIZE];

// Serial communication variables for Python script
extern uint8_t messageCounter;
extern bool processingCommand;               // Flag to pause state reporting during command processing
extern uint32_t lastCommandTime;            // Time when last command was processed

// Cup process state machine variables
extern CupState cupState;
extern uint32_t cupProcessStartTime;
extern uint32_t lastCupStateReport;
extern uint32_t binWaitStartTime;
extern uint32_t scanningStartTime;
extern uint32_t finalStepCount;
extern uint32_t errorStepCount;
extern uint32_t errorStopStartTime;
extern bool cupDecisionReceived;
extern bool cupOkDecision;
extern bool paymentProcessedReceived;
extern uint32_t paymentWaitStartTime;
extern bool errorProcessedReceived;
extern bool useSlowSpeed;
extern bool npnInput1WasHigh;
extern bool startStepCounting;
extern bool firstCupInserted;

// Other variables (not saved to EEPROM)
extern int32_t outsideLightAccumulator;      // Accumulator for outside light timing
extern uint32_t lastOutsideLightUpdate;     // Last time the accumulator was updated
extern uint32_t lastWaitingLedUpdate;       // Last time the waiting LED was updated
extern bool waitingLedState;                // Current state of waiting LED (on/off)

// Door status encoding for 2nd data byte:
// Bit 4: Service door (1=open, 0=closed)
// Bit 0: Bin door (1=open, 0=closed)
// Examples: 0x11=both open, 0x00=both closed, 0x01=service closed/bin open, 0x10=service open/bin closed
extern uint8_t doorStatusByte;              // Global variable to store door status

// EEPROM addresses (in bytes)
enum EEPROMVars {
    CUP_STATE_REPORT_INTERVAL_ADDR     = 0,   // uint32_t
    BIN_WAIT_TIME_ADDR                 = 4,   // uint32_t
    SCANNING_TIMEOUT_ADDR              = 8,   // uint32_t
    FINAL_STEPS_ADDR                   = 12,  // uint32_t
    MAX_STEPS_ERROR_ADDR               = 16,  // uint32_t
    ERROR_STOP_TIME_ADDR               = 20,  // uint32_t
    NORMAL_SPEED_ADDR                  = 24,  // uint32_t
    SLOW_SPEED_ADDR                    = 28,  // uint32_t
    OUTSIDE_LIGHT_MAX_RANGE_ADDR       = 32,  // uint32_t
    OUTSIDE_LIGHT_UPDATE_INTERVAL_ADDR = 36,  // uint32_t
    WAITING_LED_FLASH_INTERVAL_ADDR    = 40,  // uint32_t
    PAYMENT_TIMEOUT_ADDR               = 44,  // uint32_t
    MOVING_TO_SCANNING_POINT_TIMEOUT_ADDR = 48,  // uint32_t
    MOVING_TO_BIN_TIMEOUT_ADDR         = 52,  // uint32_t
    MOVING_TO_START_TIMEOUT_ADDR       = 56,  // uint32_t
    MOVING_TO_REVERSE_POINT_TIMEOUT_ADDR = 60,  // uint32_t
    RETURNING_TO_CUSTOMER_TIMEOUT_ADDR = 64,  // uint32_t
    ERROR_RECOVERY_TIMEOUT_ADDR        = 68,  // uint32_t
    OUTSIDE_LIGHT_ENABLED_ADDR         = 72,  // bool (1 byte)
    INSIDE_LED_ENABLED_ADDR            = 73,  // bool (1 byte)
    SECURITY_STATE_ADDR                = 74   // bool (1 byte)
    // Next free address: 75
};

#endif // EEPROM_CONFIG_H
