#pragma once

#include <Arduino.h>
#include "../commands/commands.h"
#include "protocol.h"

class PacketHandler {
public:
    PacketHandler();
    
    // Process a single byte of incoming data
    void processIncomingByte(uint8_t byte);
    
    // Check for timeouts and do periodic tasks
    void update(uint32_t currentTime);
    
    // Register command handlers
    void registerHandler(Command cmd, CommandHandler* handler);

private:
    // Buffer management
    uint8_t rxBuffer[MAX_PACKET_SIZE];
    volatile uint8_t rxIndex;
    
    // Timing
    uint32_t lastByteTime;
    uint32_t lastHeartbeat;
    static const uint32_t TIMEOUT_MS = 1000;
    static const uint32_t HEARTBEAT_MS = 5000;
    
    // Command handlers
    CommandHandler* handlers[10];  // Support for commands 0-9
    
    // Internal methods
    void resetBuffer();
    void handleTimeout(uint32_t currentTime);
    void handleHeartbeat(uint32_t currentTime);
    void processPacket();
    bool validatePacketLength(uint8_t length);
}; 