#include "global_variables.h"

// ===== GLOBAL VARIABLE DEFINITIONS =====

// EEPROM startup configuration
const EEPROMStartup eepromStartup = EEPROMStartup::DO_NOTHING;      // choose what to do with EEPROM at startup

// Cup process timing variables
uint32_t CUP_STATE_REPORT_INTERVAL = 500;   // Send cup state every *** milliseconds
uint32_t BIN_WAIT_TIME = 1000;              // Wait *** microsecond at bin
uint32_t SCANNING_TIMEOUT = 60000;          // *** microseconds timeout for scanning (60000 ms)
uint32_t FINAL_STEPS = 50;                  // *** steps after NPN_INPUT_1 turns off
uint32_t MAX_STEPS_ERROR = 4000;            // Maximum steps before error
uint32_t ERROR_STOP_TIME = 2000;            // *** microseconds stop during error recovery
uint32_t NORMAL_SPEED = 2000;               // *** microseconds (normal speed)
uint32_t SLOW_SPEED = 2000;                 // *** microseconds (slow speed)

// Cup state timeouts (in milliseconds)
uint32_t MOVING_TO_SCANNING_POINT_TIMEOUT = 2000;  // *** microseconds timeout for moving to scanning point
uint32_t MOVING_TO_BIN_TIMEOUT = 2000;             // *** microseconds timeout for moving to bin
uint32_t MOVING_TO_START_TIMEOUT = 4000;           // *** microseconds timeout for moving to start
uint32_t MOVING_TO_REVERSE_POINT_TIMEOUT = 2000;   // *** microseconds timeout for moving to reverse point
uint32_t RETURNING_TO_CUSTOMER_TIMEOUT = 4000;     // *** microseconds timeout for returning to customer
uint32_t ERROR_RECOVERY_TIMEOUT = 4000;            // *** microseconds timeout for error recovery movement

// Outside light variables
int32_t OUTSIDE_LIGHT_MAX_RANGE = 200;      // Maximum range for light timer accumulator
uint32_t OUTSIDE_LIGHT_UPDATE_INTERVAL = 1000; // Update accumulator every *** microseconds
bool OUTSIDE_LIGHT_ENABLED = true;

// Inside LED variables
bool INSIDE_LED_ENABLED = true;

// RGB LED flashing variables for WAITING_FOR_CUP state
uint32_t WAITING_LED_FLASH_INTERVAL = 1000; // Flash interval in *** microseconds

// Payment timeout
uint32_t PAYMENT_TIMEOUT = 60000;           // Payment timeout in milliseconds (60 seconds)

// Security variables
bool SECURITY_STATE = true;                  // If true, doors status is sent to python script

// Communication buffer for responses
uint8_t txBuffer[MAX_PACKET_SIZE];

// Serial communication variables for Python script
uint8_t messageCounter = 0;
bool processingCommand = false;              // Flag to pause state reporting during command processing
uint32_t lastCommandTime = 0;               // Time when last command was processed

// Cup process state machine variables
CupState cupState = CupState::WAITING_FOR_CUP;
uint32_t cupProcessStartTime = 0;
uint32_t lastCupStateReport = 0;
uint32_t binWaitStartTime = 0;
uint32_t scanningStartTime = 0;
uint32_t finalStepCount = 0;
uint32_t errorStepCount = 0;
uint32_t errorStopStartTime = 0;
bool cupDecisionReceived = false;
bool cupOkDecision = false;
bool paymentProcessedReceived = false;
uint32_t paymentWaitStartTime = 0;
bool errorProcessedReceived = false;
bool useSlowSpeed = false;
bool npnInput1WasHigh = false;
bool startStepCounting = false;
bool firstCupInserted = false;

// Other variables (not saved to EEPROM)
int32_t outsideLightAccumulator = 0;         // Accumulator for outside light timing
uint32_t lastOutsideLightUpdate = 0;        // Last time the accumulator was updated
uint32_t lastWaitingLedUpdate = 0;          // Last time the waiting LED was updated
bool waitingLedState = false;               // Current state of waiting LED (on/off)

// Door status encoding for 2nd data byte:
// Bit 4: Service door (1=open, 0=closed)
// Bit 0: Bin door (1=open, 0=closed)
// Examples: 0x11=both open, 0x00=both closed, 0x01=service closed/bin open, 0x10=service open/bin closed
uint8_t doorStatusByte = 0x00;              // Global variable to store door status
