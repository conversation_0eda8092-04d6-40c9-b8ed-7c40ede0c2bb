#include "system.h"
#include "../utils/debug.h"
#include "../utils/system_utils.h"

void SystemCommandHandler::handleCommand(uint8_t sensorId, const uint8_t* data, uint8_t length) {
    DEBUG_PRINTLN("Jump to bootloader command received");

    // Send success response
    uint8_t status = 1;  // Success response
    sendResponse(sensorId, &status, 1);

    // Give time for response to be sent
    delay(100);

    // Request bootloader and reset
    // requestBootloaderAndReset();
    jumpToBootloader();
    // Jump_App();
}




    