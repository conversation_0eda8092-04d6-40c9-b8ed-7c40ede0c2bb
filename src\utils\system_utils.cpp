#include "system_utils.h"
#include "debug.h"
#include <FlashStorage_STM32.h>
#include "stm32l5xx_hal.h"


// Pin definitions (from main.cpp)
#define INPUT_2             Pinout::Inputs::Current::IN_2           // Service door sensor (0 - open, 1 - closed)
#define BIN_DOOR_SENSOR     Pinout::Doors::Current::BIN_DOOR_SENSOR // Bin door build in sensor (0 - closed, 1 - open)

void sendDataToPython() {
    uint8_t message[MAX_PACKET_SIZE];
    uint8_t idx = 0;

    // Start byte
    message[idx++] = START_BYTE;

    // Command byte (using custom command 0xFE for cup state)
    message[idx++] = 0xFE;

    // Prepare payload data
    uint8_t payload[4];

    // First data byte: cup state code
    switch (cupState) {
        case CupState::WAITING_FOR_CUP: payload[0] = 0x01; break;
        case CupState::MOVING_TO_SCANNING_POINT: payload[0] = 0x02; break;
        case CupState::SCANNING: payload[0] = 0x03; break;
        case CupState::MOVING_TO_BIN: payload[0] = 0x04; break;
        case CupState::UNLOADING_AT_BIN: payload[0] = 0x05; break;
        case CupState::WAITING_FOR_PAYMENT: payload[0] = 0x06; break;
        case CupState::MOVING_TO_START: payload[0] = 0x07; break;
        case CupState::MOVING_TO_REVERSE_POINT: payload[0] = 0x08; break;
        case CupState::RETURNING_TO_CUSTOMER: payload[0] = 0x09; break;
        case CupState::ERROR_RECOVERY: payload[0] = 0x0A; break;
        case CupState::ERROR: payload[0] = 0x0B; break;
        default: payload[0] = 0x00; break;
    }

    // Second data byte: door status (if security state is enabled)
    if (SECURITY_STATE) {
        payload[1] = doorStatusByte;
    } else {
        payload[1] = 0x00;
    }

    // Other unused data bytes set to 0x00
    payload[2] = 0x00;
    payload[3] = 0x00;

    // Length byte (payload + CRC + END)
    uint8_t payloadLength = sizeof(payload);
    message[idx++] = payloadLength + 3;   // +3 for CRC(2) + END(1)

    // Add payload
    for (uint8_t i = 0; i < payloadLength; i++) {
        message[idx++] = payload[i];
    }

    // Calculate CRC16 for the packet so far
    uint16_t crc = calculateCRC16(message, idx);

    // Add CRC bytes
    message[idx++] = (crc >> 8) & 0xFF;   // CRC high byte
    message[idx++] = crc & 0xFF;          // CRC low byte

    // End byte
    message[idx++] = END_BYTE;

    // Send message to Python script via Serial1
    // Clear any pending data first to avoid interference
    Serial1.flush();
    delay(1);  // Small delay to ensure line is clear

    // Send complete message at once for better timing
    Serial1.write(message, idx);
    Serial1.flush();  // Ensure all data is sent
}

void doors() {
    // Debug printing
    // Serial2.println("Service door separate sensor: " + String(!digitalRead(INPUT_2)) + ", Service door build in sensor: " + String(digitalRead(SERVICE_DOOR_SENSOR)) + ", Bin door build in sensor: " + String(digitalRead(BIN_DOOR_SENSOR)));

    // Only process door status if security state is enabled
    if (SECURITY_STATE) {

        bool serviceDoorOpen = !digitalRead(INPUT_2);               // Separate service door sensor
        // bool serviceDoorOpen = digitalRead(SERVICE_DOOR_SENSOR);    // Build in service door sensor
        bool binDoorOpen = digitalRead(BIN_DOOR_SENSOR);            // Build in bin door sensor

        // Encode door status into single byte
        // First half-byte (bits 4-7): service door status
        // Second half-byte (bits 0-3): bin door status
        // 1 = open, 0 = closed
        doorStatusByte = 0x00;

        if (serviceDoorOpen) {
            doorStatusByte |= 0x10;  // Set bit 4 for service door open
        }
        if (binDoorOpen) {
            doorStatusByte |= 0x01;  // Set bit 0 for bin door open
        }
    }
}

void setupEEPROM() {
    switch (eepromStartup) {
        case EEPROMStartup::REWRITE:
            // Write initial values to EEPROM
            EEPROM.put(CUP_STATE_REPORT_INTERVAL_ADDR, CUP_STATE_REPORT_INTERVAL);
            EEPROM.put(BIN_WAIT_TIME_ADDR, BIN_WAIT_TIME);
            EEPROM.put(SCANNING_TIMEOUT_ADDR, SCANNING_TIMEOUT);
            EEPROM.put(FINAL_STEPS_ADDR, FINAL_STEPS);
            EEPROM.put(MAX_STEPS_ERROR_ADDR, MAX_STEPS_ERROR);
            EEPROM.put(ERROR_STOP_TIME_ADDR, ERROR_STOP_TIME);
            EEPROM.put(NORMAL_SPEED_ADDR, NORMAL_SPEED);
            EEPROM.put(SLOW_SPEED_ADDR, SLOW_SPEED);
            EEPROM.put(OUTSIDE_LIGHT_MAX_RANGE_ADDR, OUTSIDE_LIGHT_MAX_RANGE);
            EEPROM.put(OUTSIDE_LIGHT_UPDATE_INTERVAL_ADDR, OUTSIDE_LIGHT_UPDATE_INTERVAL);
            EEPROM.put(WAITING_LED_FLASH_INTERVAL_ADDR, WAITING_LED_FLASH_INTERVAL);
            EEPROM.put(PAYMENT_TIMEOUT_ADDR, PAYMENT_TIMEOUT);
            EEPROM.put(OUTSIDE_LIGHT_ENABLED_ADDR, OUTSIDE_LIGHT_ENABLED);
            EEPROM.put(INSIDE_LED_ENABLED_ADDR, INSIDE_LED_ENABLED);
            EEPROM.put(SECURITY_STATE_ADDR, SECURITY_STATE);
            EEPROM.commit(); // Commit changes to flash
            break;
        case EEPROMStartup::LOAD_FROM_IT:
            // Load initial values from EEPROM
            EEPROM.get(CUP_STATE_REPORT_INTERVAL_ADDR, CUP_STATE_REPORT_INTERVAL);
            EEPROM.get(BIN_WAIT_TIME_ADDR, BIN_WAIT_TIME);
            EEPROM.get(SCANNING_TIMEOUT_ADDR, SCANNING_TIMEOUT);
            EEPROM.get(FINAL_STEPS_ADDR, FINAL_STEPS);
            EEPROM.get(MAX_STEPS_ERROR_ADDR, MAX_STEPS_ERROR);
            EEPROM.get(ERROR_STOP_TIME_ADDR, ERROR_STOP_TIME);
            EEPROM.get(NORMAL_SPEED_ADDR, NORMAL_SPEED);
            EEPROM.get(SLOW_SPEED_ADDR, SLOW_SPEED);
            EEPROM.get(OUTSIDE_LIGHT_MAX_RANGE_ADDR, OUTSIDE_LIGHT_MAX_RANGE);
            EEPROM.get(OUTSIDE_LIGHT_UPDATE_INTERVAL_ADDR, OUTSIDE_LIGHT_UPDATE_INTERVAL);
            EEPROM.get(WAITING_LED_FLASH_INTERVAL_ADDR, WAITING_LED_FLASH_INTERVAL);
            EEPROM.get(PAYMENT_TIMEOUT_ADDR, PAYMENT_TIMEOUT);
            EEPROM.get(OUTSIDE_LIGHT_ENABLED_ADDR, OUTSIDE_LIGHT_ENABLED);
            EEPROM.get(INSIDE_LED_ENABLED_ADDR, INSIDE_LED_ENABLED);
            EEPROM.get(SECURITY_STATE_ADDR, SECURITY_STATE);
            break;
        case EEPROMStartup::DO_NOTHING:
            // Do nothing
            break;
    }
}





// This code can be used if you wan to tre 
// #define BOOTLOADER_FLAG_ADDR 0x2003F000U  // SRAM2 oblast (na konci RAM)

// void requestBootloaderAndReset() {
//     *((volatile uint32_t *)BOOTLOADER_FLAG_ADDR) = 0xDEADBEEFU;
//     __DSB();
//     NVIC_SystemReset();
// }

// bool checkBootloaderRequest() {
//     if (*((volatile uint32_t *)BOOTLOADER_FLAG_ADDR) == 0xDEADBEEFU) {
//         *((volatile uint32_t *)BOOTLOADER_FLAG_ADDR) = 0;
//         HAL_PWR_DisableBkUpAccess();
//         return true;
//     }
//     return false;
// }

// void clearRAM() {
//     uint32_t* p = (uint32_t*)SRAM_START;
//     uint32_t* end = (uint32_t*)(SRAM_START + SRAM_SIZE);

//     while (p < end) {
//         *p++ = 0;
//     }
// }



#define SYSMEM_BOOTLOADER_ADDR 0x0BF90000U

void jumpToBootloader() {

    typedef void (*pFunction)(void);
    uint32_t bootAddr = SYSMEM_BOOTLOADER_ADDR;

    // Odpojit systick
    SysTick->CTRL = 0;
    SysTick->LOAD = 0;
    SysTick->VAL  = 0;

    // Zakazat preruseni
    // Disa
    __disable_irq();

    // Reset hours and peripherals
    HAL_RCC_DeInit();
    HAL_DeInit();

    // Vypnout vsechny IRQ v NVIC
    for (int i = 0; i < 8; i++) {
        NVIC->ICER[i] = 0xFFFFFFFF;
        NVIC->ICPR[i] = 0xFFFFFFFF;
    }

    __enable_irq();

    
    // Skocit na reset handler bootloaderu
    pFunction boot_jump = (pFunction) (*(volatile uint32_t*) (bootAddr + 4));
    
    // Prepnout stack pointer na bootloader
    __set_MSP(*(volatile uint32_t *) bootAddr);
    
    boot_jump();

    // sem se uz nikdy nevratime
}