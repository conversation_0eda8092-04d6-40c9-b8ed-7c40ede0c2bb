#pragma once

#include <Arduino.h>

enum class BoardRevision {
    REV_A,
    REV_B,
};

static constexpr BoardRevision CURRENT_BOARD_REVISION = BoardRevision::REV_A; // NOW!

class Pinout {
public:
    static Pinout& getInstance() {
        static Pinout instance;
        return instance;
    }

    struct Doors {
        struct RevA {
            static constexpr uint8_t SERVICE_DOOR = PE9; // Service door
            static constexpr uint8_t BIN_DOOR = PE12; // Bin
            static constexpr uint8_t RELAY = PE13; // High Current relay
            static constexpr uint8_t SERVICE_DOOR_SENSOR = PE8; // Service door sensor
            static constexpr uint8_t BIN_DOOR_SENSOR = PE11; // Bin door sensor
        };

        using Current = RevA;

    };

    struct Inputs {
        struct RevA {
            static constexpr uint8_t IN_1 = PB2; 
            static constexpr uint8_t IN_2 = PE7; 
            static constexpr uint8_t IN_3 = PB0; 
            static constexpr uint8_t IN_4 = PB1; 
            static constexpr uint8_t IN_5 = PA4; 
            static constexpr uint8_t IN_6 = PA5; 
        };

        using Current = RevA;
    };

    struct Inputs_NPN {
        struct RevA {
            static constexpr uint8_t IN_1 = PE15; 
            static constexpr uint8_t IN_2 = PE14; 
            static constexpr uint8_t IN_3 = PB14; 
            static constexpr uint8_t IN_4 = PB13; 
            static constexpr uint8_t IN_5 = PD10; 
            static constexpr uint8_t IN_6 = PD9; 

        };

        using Current = RevA;
    };

    struct LEDs {
        struct RevA {
            static constexpr uint8_t INSIDE_LED = PC8;
            static constexpr uint8_t OUTSIDE_LED = PD11;
            static constexpr uint8_t LED_RED = PB15;
            static constexpr uint8_t LED_GREEN = PB5;
            static constexpr uint8_t LED_ON = PE10;
        };

        using Current = RevA;
    };

    static constexpr BoardRevision getCurrentRevision() {
        return CURRENT_BOARD_REVISION;
    }

    static const char* getCurrentRevisionString() {
        switch (CURRENT_BOARD_REVISION) {
            case BoardRevision::REV_A: return "REV_A";
            case BoardRevision::REV_B: return "REV_B";
            default: return "UNKNOWN";
        }
    }

    struct Motor {
        struct RevA {
            static constexpr uint8_t STEP_PIN = PE3;
            static constexpr uint8_t DIR_PIN = PE6;
            static constexpr uint8_t EN_PIN = PE5;
        };
        
        using Current = RevA;
    };

private:
    Pinout() {}
    Pinout(const Pinout&) = delete;
    Pinout& operator=(const Pinout&) = delete;
};

#define BOARD_PINOUT Pinout::getInstance()

/* Example usage in code:

#include "config/pinout.h"

void setup() {
    auto& pinout = BOARD_PINOUT;
    
    Serial.println(pinout.getCurrentRevisionString());
    
    pinMode(pinout.Doors.SERVICE_DOOR, OUTPUT);
    pinMode(pinout.Doors.BIN_DOOR, OUTPUT);
    pinMode(pinout.Doors.RELAY, OUTPUT);
}

*/ 