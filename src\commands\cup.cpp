#include "cup.h"
#include "../utils/debug.h"
#include "../config/global_variables.h"

// Global variable to track the current command being processed
extern uint8_t currentCommand;

void CupCommandHandler::handleCommand(uint8_t sensorId, const uint8_t* data, uint8_t length) {
    // Check which cup command was received based on the global currentCommand
    if (currentCommand == CMD_CUP_OK) {
        DEBUG_PRINTLN("CUP OK received");
        cupDecisionReceived = true;
        cupOkDecision = true;
    } else if (currentCommand == CMD_CUP_NOT_OK) {
        DEBUG_PRINTLN("CUP NOT OK received");
        cupDecisionReceived = true;
        cupOkDecision = false;
    } else if (currentCommand == CMD_ERROR_PROCESSED) {
        DEBUG_PRINTLN("ERROR PROCESSED received");
        errorProcessedReceived = true;
    } else if (currentCommand == CMD_PAYMENT_PROCESSED) {
        DEBUG_PRINTLN("PAYMENT PROCESSED received");
        paymentProcessedReceived = true;
    } else {
        DEBUG_PRINTLN("Unknown cup command received");
    }

    // Send success response (similar to unlock_door)
    uint8_t status = 1;  // Success response
    sendResponse(sensorId, &status, 1);
}
