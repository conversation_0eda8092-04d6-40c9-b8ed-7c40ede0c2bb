#include "door.h"
#include "../utils/debug.h"
#include "../config/pinout.h"

void DoorCommandHandler::handleCommand(uint8_t doorId, const uint8_t* data, uint8_t length) {
    Serial2.print("TEST CALLBACK - Received unlock command for door ID: ");
    Serial2.println(doorId);
    
    if (doorId > BIN_DOOR_ID) {
        DEBUG_PRINTLN("Invalid door ID!");
        uint8_t status = 0;  // Error response
        sendResponse(doorId, &status, 1);
        return;
    }
    
    bool success = unlockDoor(doorId);
    uint8_t status = success ? 1 : 0;
    sendResponse(doorId, &status, 1);
}

bool DoorCommandHandler::unlockDoor(uint8_t doorId) {
    switch(doorId) {
        case SERVICE_DOOR_ID: {
            // Activate service door and relay
            digitalWrite(Pinout::Doors::Current::SERVICE_DOOR, HIGH);
            digitalWrite(Pinout::Doors::Current::RELAY, HIGH);
            
            // Wait 40ms
            delay(40);
            
            // Deactivate both
            digitalWrite(Pinout::Doors::Current::SERVICE_DOOR, LOW);
            digitalWrite(Pinout::Doors::Current::RELAY, LOW);
            
            DEBUG_PRINTLN("Service door WERE unlocked");
            return true;
        }
        case BIN_DOOR_ID:
            // Activate service door and relay
            digitalWrite(Pinout::Doors::Current::BIN_DOOR, HIGH);
            digitalWrite(Pinout::Doors::Current::RELAY, HIGH);
            
            // Wait 40ms
            delay(40);
            
            // Deactivate both
            digitalWrite(Pinout::Doors::Current::BIN_DOOR, LOW);
            digitalWrite(Pinout::Doors::Current::RELAY, LOW);
            
            DEBUG_PRINTLN("Bin door WERE unlocked");
            return true;
    }
    return false;
} 