#include "temperature.h"
#include "../utils/debug.h"

void TemperatureCommandHandler::handleCommand(uint8_t sensorId, const uint8_t* data, uint8_t length) {
    DEBUG_PRINTLN("\n=== Reading Temperature ===");
    DEBUG_PRINT("Sensor ID: ");
    Serial2.println(sensorId);

    if (sensorId > 2) {
        DEBUG_PRINTLN("Invalid sensor ID!");
        uint8_t errorData[] = {0};  // Error response
        sendResponse(sensorId, errorData, 1);
        return;
    }

    float temp = readTemperature(sensorId);
    DEBUG_PRINT("Raw temperature value: ");
    Serial2.println(temp);
    
    // Convert float to fixed point (multiply by 10 to preserve one decimal place)
    int16_t tempFixed = (int16_t)(temp * 10.0f);
    DEBUG_PRINT("Fixed point value (x10): ");
    Serial2.println(tempFixed);
    
    uint8_t tempData[2];
    tempData[0] = (uint8_t)((tempFixed >> 8) & 0xFF);    // High byte
    tempData[1] = (uint8_t)(tempFixed & 0xFF);           // Low byte
    
    DEBUG_PRINT("High byte: 0x");
    DEBUG_PRINT_HEX(tempData[0]);
    DEBUG_PRINT(" Low byte: 0x");
    DEBUG_PRINT_HEX(tempData[1]);
    Serial2.println();
    
    DEBUG_PRINTLN("Sending response...");
    sendResponse(sensorId, tempData, 2);  // Send 2 bytes of temperature data
    DEBUG_PRINTLN("=== End Temperature Reading ===\n");
}

float TemperatureCommandHandler::readTemperature(uint8_t sensorId) {
    return (sensorId == 1) ? TEMP_SENSOR_1 : TEMP_SENSOR_2;
} 