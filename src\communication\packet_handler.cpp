#include "packet_handler.h"
#include "../utils/debug.h"

// Global variable to track current command for cup handler
uint8_t currentCommand = 0;

PacketHandler::PacketHandler() : rxIndex(0), lastByteTime(0), lastHeartbeat(0) {
    // Initialize handlers array to nullptr
    for(int i = 0; i < 10; i++) {
        handlers[i] = nullptr;
    }
}

void PacketHandler::registerHandler(Command cmd, CommandHandler* handler) {
    if(static_cast<int>(cmd) < 10) {
        handlers[static_cast<int>(cmd)] = handler;
    }
}

void PacketHandler::processIncomingByte(uint8_t byte) {
    // Enhanced debug output
    DEBUG_PRINT("\nReceived byte [");
    Serial2.print(rxIndex);
    DEBUG_PRINT("]: 0x");
    DEBUG_PRINT_HEX(byte);
    DEBUG_PRINT(" (");
    if (byte >= 32 && byte <= 126) {
        Serial2.write(byte); // Print ASCII if printable
    } else {
        Serial2.print(".");
    }
    DEBUG_PRINTLN(")");

    // Handle start of new packet
    if (rxIndex == 0) {
        if (byte == START_BYTE) {
            rxBuffer[rxIndex++] = byte;
            DEBUG_PRINTLN("Start byte detected - beginning new packet");
        } else {
            DEBUG_PRINT("Ignoring non-start byte: 0x");
            DEBUG_PRINT_HEX(byte);
            Serial2.println();
        }
        return;
    }

    // Store byte
    rxBuffer[rxIndex++] = byte;
    DEBUG_PRINT("Current buffer state: ");
    DEBUG_PACKET("", rxBuffer, rxIndex);

    // Check if we have enough bytes for length
    if (rxIndex == 3) {
        if (!validatePacketLength(rxBuffer[2])) {
            resetBuffer();
            return;
        }
    }

    // Check if we have a complete packet
    if (rxIndex >= 3) {
        uint8_t expectedLength = rxBuffer[2] + 3;
        
        if (rxIndex >= expectedLength) {
            if (rxBuffer[rxIndex - 1] == END_BYTE) {
                DEBUG_PRINTLN("End byte found - processing packet");
                processPacket();
            } else {
                DEBUG_PRINTLN("ERROR: Invalid end byte!");
                DEBUG_PRINT("Expected: 0x");
                DEBUG_PRINT_HEX(END_BYTE);
                DEBUG_PRINT(" Got: 0x");
                DEBUG_PRINT_HEX(rxBuffer[rxIndex - 1]);
                Serial2.println();
            }
            resetBuffer();
        }
    }
}

void PacketHandler::update(uint32_t currentTime) {
    handleTimeout(currentTime);
    handleHeartbeat(currentTime);
}

void PacketHandler::resetBuffer() {
    rxIndex = 0;
}

bool PacketHandler::validatePacketLength(uint8_t length) {
    uint8_t expectedLength = length + 3;
    DEBUG_PRINTLN("\n=== Length Byte Received ===");
    DEBUG_PRINT("Claimed payload length: ");
    Serial2.println(length);
    DEBUG_PRINT("Expected total length: ");
    Serial2.println(expectedLength);
    
    if (expectedLength > MAX_PACKET_SIZE) {
        DEBUG_PRINTLN("ERROR: Packet too long!");
        DEBUG_PRINT("Max allowed: ");
        Serial2.println(MAX_PACKET_SIZE);
        return false;
    }
    return true;
}

void PacketHandler::handleTimeout(uint32_t currentTime) {
    if (rxIndex > 0 && (currentTime - lastByteTime) > TIMEOUT_MS) {
        DEBUG_PRINTLN("\n=== TIMEOUT DETECTED ===");
        DEBUG_PRINT("Time since last byte: ");
        Serial2.print((currentTime - lastByteTime));
        DEBUG_PRINTLN(" ms");
        DEBUG_PRINT("Incomplete packet size: ");
        Serial2.println(rxIndex);
        DEBUG_PACKET("Abandoned packet", rxBuffer, rxIndex);
        resetBuffer();
        DEBUG_PRINTLN("Buffer cleared");
        DEBUG_PRINTLN("=== End Timeout ===\n");
    }
}

void PacketHandler::handleHeartbeat(uint32_t currentTime) {
    if (currentTime - lastHeartbeat >= HEARTBEAT_MS) {
        // DEBUG_PRINTLN("\n=== Heartbeat ===");
        // DEBUG_PRINT("Uptime: ");
        // Serial2.print(currentTime / 1000);
        // DEBUG_PRINTLN(" seconds");
        // DEBUG_PRINT("Serial1 available bytes: ");
        // Serial2.println(Serial1.available());
        // if (rxIndex > 0) {
        //     DEBUG_PRINTLN("WARNING: Partial packet in buffer:");
        //     DEBUG_PACKET("Current buffer", rxBuffer, rxIndex);
        // } else {
        //     DEBUG_PRINTLN("Buffer empty - waiting for commands...");
        // }
        // DEBUG_PRINTLN("=== End Heartbeat ===\n");
        lastHeartbeat = currentTime;
    }
}

void PacketHandler::processPacket() {
    Command cmd = static_cast<Command>(rxBuffer[1]);
    uint8_t sensorId = rxBuffer[3];
    uint8_t* data = &rxBuffer[4];
    uint8_t dataLen = rxBuffer[2] - 3;

    DEBUG_PRINT("Processing command: ");
    Serial2.println(static_cast<int>(cmd));

    // Set global command for cup handler
    currentCommand = static_cast<uint8_t>(cmd);

    // Find and execute appropriate handler
    if(static_cast<int>(cmd) < 10 && handlers[static_cast<int>(cmd)] != nullptr) {
        handlers[static_cast<int>(cmd)]->handleCommand(sensorId, data, dataLen);
    } else {
        DEBUG_PRINTLN("Invalid command or handler not registered");
        DEBUG_PRINT("Command value: ");
        Serial2.println(static_cast<int>(cmd));
    }
}