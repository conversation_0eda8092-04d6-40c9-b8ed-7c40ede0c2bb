#include <Wire.h>
// #include <Arduino.h>
#include <IWatchdog.h>
#include <MCP23017.h>
#include <HardwareTimer.h>
#include "utils/debug.h"
#include "communication/protocol.h"
#include "communication/packet_handler.h"
#include "commands/commands.h"
#include "commands/temperature.h"
#include "commands/input.h"
#include "commands/door.h"
#include "commands/motor.h"
#include "commands/cup.h"
#include "commands/system.h"
#include "config/pinout.h"
#include "config/global_variables.h"
#include "types/cup_types.h"
#include "hardware/motor_control.h"
#include "hardware/led_control.h"
#include "hardware/cup_control.h"
#include "utils/system_utils.h"



// Serial port definitions
HardwareSerial Serial1(USART1);  // Data communication
HardwareSerial Serial2(USART2);  // Debug output

// Handlers
PacketHandler packetHandler;
TemperatureCommandHandler tempHandler;
InputCommandHandler inputHandler;
DoorCommandHandler doorHandler;
MotorCommandHandler motorHandler;
CupCommandHandler cupHandler;
SystemCommandHandler systemHandler;

// MCP23017 instance
MCP23017 mcp = MCP23017(0x20);  // Address 0x20 (all address pins to GND) 

// I2C pins mapping
#define SDA_PIN     PB7
#define SCL_PIN     PB8

// Motor pin definitions moved to motor/motor_control.h and motor/motor_control.cpp

// Doors pin mapping
#define RELAY               Pinout::Doors::Current::RELAY
#define SERVICE_DOOR        Pinout::Doors::Current::SERVICE_DOOR
#define BIN_DOOR            Pinout::Doors::Current::BIN_DOOR
#define SERVICE_DOOR_SENSOR Pinout::Doors::Current::SERVICE_DOOR_SENSOR  // Service door build in sensor (0 - closed, 1 - open)
#define BIN_DOOR_SENSOR     Pinout::Doors::Current::BIN_DOOR_SENSOR      // Bin door build in sensor (0 - closed, 1 - open)

// LED pins are now defined in hardware/led_control.h

// NPN input pins are now defined in hardware/cup_control.h

// input pins mapping
#define INPUT_1             Pinout::Inputs::Current::IN_1           // nothing connected
#define INPUT_2             Pinout::Inputs::Current::IN_2           // Service door sensor (0 - open, 1 - closed)
#define INPUT_3             Pinout::Inputs::Current::IN_3           // Light sensor (0 = dark, 1 = bright)
#define INPUT_4             Pinout::Inputs::Current::IN_4           // nothing connected
#define INPUT_5             Pinout::Inputs::Current::IN_5           // nothing connected
#define INPUT_6             Pinout::Inputs::Current::IN_6           // nothing connected



void setup() {


    // Initialize Watchdog first
    if (IWatchdog.isReset()) {
        DEBUG_PRINTLN("WARNING: System reset by watchdog!");
    }
    IWatchdog.begin(45000000); // 45 second timeout


    // EEPROM setup
    setupEEPROM();

    // Initialize MCP23017
    pinMode(SDA_PIN, INPUT_PULLUP); 
    pinMode(SCL_PIN, INPUT_PULLUP); 
    Wire.setSDA(SDA_PIN); 
    Wire.setSCL(SCL_PIN); 
    Wire.begin(); 
    Wire.setClock(100000); 
    mcp.init();

    // Initialize NPN input pins
    pinMode(NPN_INPUT_1, INPUT);
    pinMode(NPN_INPUT_2, INPUT);
    pinMode(NPN_INPUT_3, INPUT);
    pinMode(NPN_INPUT_4, INPUT);
    pinMode(NPN_INPUT_5, INPUT);
    pinMode(NPN_INPUT_6, INPUT);

    // initialize input pins
    pinMode(INPUT_1, INPUT);
    pinMode(INPUT_2, INPUT);
    pinMode(INPUT_3, INPUT);
    pinMode(INPUT_4, INPUT);
    pinMode(INPUT_5, INPUT);
    pinMode(INPUT_6, INPUT);

    // Initialize all LED pins using dedicated function
    LEDControl::initializeLeds();


    /*
    * MCP23017 Pin Configuration:
    * 
    * Port A:
    * GPA0 - Not connected (configured as output)
    * GPA1 - DIAG0 (output)
    * GPA2 - DIAG1 (output)
    * GPA3 - DC0 (output)
    * GPA4 - Not connected (configured as input)
    * GPA5 - EN (input, set to HIGH)
    * GPA6 - DIR (input, set to LOW)
    * GPA7 - Input (set to LOW)
    * 
    * Direction register (IODIR_A): 0xE0
    * - Inputs (1): GPA7, GPA6, GPA5, GPA4
    * - Outputs (0): GPA3, GPA2, GPA1, GPA0
    * 
    * Initial values (GPIO_A): 0x20
    * - HIGH (1): GPA5 (EN)
    * - LOW (0): All other pins
    */
   
    // Configure Port A
    mcp.writeRegister(MCP23017Register::IODIR_A, 0xE0);  // Set direction (1=input, 0=output)
    mcp.writeRegister(MCP23017Register::GPIO_A, 0x20);   // Set initial values (EN=HIGH, DIR=LOW, GPA7=LOW)

    // Configure Port B - all pins input and HIGH // FOR NOW NOT USED
    mcp.writeRegister(MCP23017Register::IODIR_B, 0xFF);  // All pins as inputs
    mcp.writeRegister(MCP23017Register::GPIO_B, 0xFF);   // All pins HIGH (enable pull-ups)



    // Initialize input pins
    pinMode(Pinout::Inputs::Current::IN_1, INPUT);
    pinMode(Pinout::Inputs::Current::IN_2, INPUT);
    pinMode(Pinout::Inputs::Current::IN_3, INPUT);
    pinMode(Pinout::Inputs::Current::IN_4, INPUT);
    pinMode(Pinout::Inputs::Current::IN_5, INPUT);
    pinMode(Pinout::Inputs::Current::IN_6, INPUT);

    // Initialize input pins
    pinMode(Pinout::Inputs_NPN::RevA::IN_1, INPUT);
    pinMode(Pinout::Inputs_NPN::RevA::IN_2, INPUT);
    pinMode(Pinout::Inputs_NPN::RevA::IN_3, INPUT);
    pinMode(Pinout::Inputs_NPN::RevA::IN_4, INPUT);
    pinMode(Pinout::Inputs_NPN::RevA::IN_5, INPUT);
    pinMode(Pinout::Inputs_NPN::RevA::IN_6, INPUT);

    // Initialize door pins
    pinMode(RELAY, OUTPUT);
    pinMode(SERVICE_DOOR, OUTPUT);
    pinMode(BIN_DOOR, OUTPUT);
    pinMode(SERVICE_DOOR_SENSOR, INPUT);
    pinMode(BIN_DOOR_SENSOR, INPUT);

    // Initialize USART1 for data communication
    pinMode(PA9, OUTPUT);   // TX
    pinMode(PA10, INPUT);   // RX
    Serial1.setRx(PA10);
    Serial1.setTx(PA9);
    Serial1.begin(115200);

    // Initialize USART2 for debug output
    Serial2.setRx(PA3);
    Serial2.setTx(PA2);
    Serial2.begin(115200);

    delay(2000);  // Wait for serial ports to stabilize

    // Clear any pending data
    while(Serial1.available()) Serial1.read();
    while(Serial2.available()) Serial2.read();

    DEBUG_PRINTLN("\n=== System Initialization ===");
    DEBUG_PRINTLN("CupBoard Controller v1.0");
    DEBUG_PRINTLN("Debug output enabled");




    // Register command handlers
    packetHandler.registerHandler(CMD_READ_TEMPERATURE, &tempHandler);
    packetHandler.registerHandler(CMD_READ_INPUT, &inputHandler);
    packetHandler.registerHandler(CMD_UNLOCK_DOOR, &doorHandler);
    packetHandler.registerHandler(CMD_MOTOR_MOVE, &motorHandler);
    packetHandler.registerHandler(CMD_CUP_OK, &cupHandler);
    packetHandler.registerHandler(CMD_CUP_NOT_OK, &cupHandler);
    packetHandler.registerHandler(CMD_ERROR_PROCESSED, &cupHandler);
    packetHandler.registerHandler(CMD_PAYMENT_PROCESSED, &cupHandler);
    packetHandler.registerHandler(CMD_JUMP_TO_BOOTLOADER, &systemHandler);
    
    // Test USART1
    DEBUG_PRINTLN("\nTesting USART1...");
    uint8_t testData[] = {0xAA, 0x55};
    size_t written = Serial1.write(testData, sizeof(testData));
    Serial1.flush();
    
    if (written == sizeof(testData)) {
        DEBUG_PRINTLN("USART1 TX test: OK");
    } else {
        DEBUG_PRINTLN("USART1 TX test: FAILED");
    }
    
    // Configure pins - set direction register directly 
    mcp.writeRegister(MCP23017Register::IODIR_A, 0x1C);  // Set pins 5,6 as outputs (0), pins 1,2,3 as inputs (1) 
    mcp.writeRegister(MCP23017Register::GPIO_A, 0xFF);   // Set all pins high initially 

    Serial2.println("Initialization complete.");

    // Initialize motor control system
    MotorControl::initializeMotorControl();

    // Enable driver at start
    MotorControl::enable_motor();
    DEBUG_PRINTLN("=== Initialization Complete ===\n");
    
}



void loop() {
    IWatchdog.reload(); // Reset watchdog timer in main loop
    uint32_t currentTime = millis();

    // Process incoming commands from Python script
    if (Serial1.available()) {
        processingCommand = true;
        lastCommandTime = currentTime;

        while (Serial1.available()) {
            uint8_t byte = Serial1.read();
            packetHandler.processIncomingByte(byte);
        }
    }

    

    // Reset processing flag after a short delay to allow response to be sent
    if (processingCommand && (currentTime - lastCommandTime) > 50) {
        processingCommand = false;
    }

    // Send continuously data to Python script (but not while processing commands)
    if (!processingCommand && (currentTime - lastCupStateReport >= CUP_STATE_REPORT_INTERVAL)) {
        sendDataToPython();
        lastCupStateReport = currentTime;

    }

    // Cup return process
    CupBoard::cupProcess();
    // LEDControl::turnOnInsideLed();
    // LEDControl::setYellowLed();

    // Update waiting LED flashing
    LEDControl::updateWaitingLed();

    // Outside light control
    LEDControl::outsideLight();

    // Door status
    doors();

    DEBUG_PRINT("Main loop running - Cup state: ");
    DEBUG_PRINTLN(CupBoard::getCupStateString());

    // Handle timeouts and heartbeat
    packetHandler.update(currentTime);
}
