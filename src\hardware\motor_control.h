#ifndef MOTOR_CONTROL_H
#define MOTOR_CONTROL_H

#include <stdint.h>
#include <HardwareTimer.h>
#include <MCP23017.h>
#include "config/pinout.h"

// Direction of rotation of motor
enum class Direction {
    LEFT,
    RIGHT,
};

namespace MotorControl {

// Motor control variables (extern declarations)
extern HardwareTimer *stepTimer;
extern volatile bool stepEnabled;

// Motor pin definitions
#define STEP_PIN        Pinout::Motor::Current::STEP_PIN

// MCP23017 bit positions for motor control (not GPIO pins!)
#define PIN_EN          5   // MCP23017 GPA5 bit position
#define PIN_DIR         6   // MCP23017 GPA6 bit position

// Motor control functions
void initializeMotorControl();
void setMotorSpeed(uint32_t speedMicros);
void stepTimerCallback();
void enable_motor();
void disable_motor();
void set_direction(Direction direction);

} // namespace MotorControl

#endif // MOTOR_CONTROL_H
