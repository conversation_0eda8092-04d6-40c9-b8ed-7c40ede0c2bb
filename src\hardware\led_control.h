#ifndef LED_CONTROL_H
#define LED_CONTROL_H

#include <Arduino.h>
#include "../types/cup_types.h"
#include "../config/pinout.h"

// LED pin definitions
#define LED_RED             Pinout::LEDs::Current::LED_RED
#define LED_GREEN           Pinout::LEDs::Current::LED_G<PERSON><PERSON>
#define LED_ON              Pinout::LEDs::Current::LED_ON
#define INSIDE_LED          Pinout::LEDs::Current::INSIDE_LED
#define OUTSIDE_LED         Pinout::LEDs::Current::OUTSIDE_LED
#define INPUT_3             Pinout::Inputs::Current::IN_3           // Light sensor (0 = dark, 1 = bright)

// External variables that LED control needs access to
extern CupState cupState;
extern uint32_t lastWaitingLedUpdate;
extern bool waitingLedState;
extern uint32_t WAITING_LED_FLASH_INTERVAL;
extern int32_t outsideLightAccumulator;
extern uint32_t lastOutsideLightUpdate;
extern uint32_t OUTSIDE_LIGHT_UPDATE_INTERVAL;
extern int32_t OUTSIDE_LIGHT_MAX_RANGE;

namespace LEDControl {

/**
 * @brief Initialize all LED pins
 * Sets up all LED pins as outputs and initializes them to a known state
 */
void initializeLeds();

/**
 * @brief Set the color of the RGB LEDs
 * @param color The color to set (OFF, RED, GREEN, YELLOW)
 */
void setLedColor(Color color);

/**
 * @brief Update the flashing green LED during WAITING_FOR_CUP state
 * This function handles the flashing green LED pattern when the system
 * is waiting for a cup to be inserted
 */
void updateWaitingLed();

/**
 * @brief Control outside light based on light sensor
 * Reads light sensor and controls outside LED with accumulator logic
 */
void outsideLight();

/**
 * @brief Turn off both RGB LEDs
 * Convenience function to turn off both red and green LEDs
 */
void turnOffRgbLeds();

/**
 * @brief Turn on red LED only
 * Convenience function to turn on red LED and turn off green LED
 */
void setRedLed();

/**
 * @brief Turn on green LED only
 * Convenience function to turn on green LED and turn off red LED
 */
void setGreenLed();

/**
 * @brief Turn on both LEDs for yellow color
 * Convenience function to turn on both red and green LEDs for yellow
 */
void setYellowLed();

/**
 * @brief Reset waiting LED state variables
 * Resets the timing and state variables used for LED flashing
 */
void resetWaitingLedState();

/**
 * @brief Turn on inside LED
 */
void turnOnInsideLed();

/**
 * @brief Turn off inside LED
 */
void turnOffInsideLed();

/**
 * @brief Turn on outside LED
 */
void turnOnOutsideLed();

/**
 * @brief Turn off outside LED
 */
void turnOffOutsideLed();

} // namespace LEDControl

#endif // LED_CONTROL_H
