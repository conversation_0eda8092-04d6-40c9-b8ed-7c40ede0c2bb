#include "cup_control.h"

namespace CupBoard {

const char* getCupStateString() {
    switch (cupState) {
        case CupState::WAITING_FOR_CUP: return "WAITING_FOR_CUP";
        case CupState::MOVING_TO_SCANNING_POINT: return "MOVING_TO_SCANNING_POINT";
        case CupState::SCANNING: return "SCANNING";
        case CupState::MOVING_TO_BIN: return "MOVING_TO_BIN";
        case CupState::UNLOADING_AT_BIN: return "UNLOADING_AT_BIN";
        case CupState::WAITING_FOR_PAYMENT: return "WAITING_FOR_PAYMENT";
        case CupState::MOVING_TO_START: return "MOVING_TO_START";
        case CupState::MOVING_TO_REVERSE_POINT: return "MOVING_TO_REVERSE_POINT";
        case CupState::RETURNING_TO_CUSTOMER: return "RETURNING_TO_CUSTOMER";
        case CupState::ERROR_RECOVERY: return "ERROR_RECOVERY";
        case CupState::ERROR: return "ERROR";
        default: return "UNKNOWN";
    }
}

void cupProcess() {
    uint32_t currentTime = millis();

    // Possible starting positions
    // NPN_INPUT_2 (scanning point) or NPN_INPUT_3 (reverse point) -> return cup to customer
    if (!firstCupInserted) {
        if (digitalRead(NPN_INPUT_2)) {
            // Cup at scanning point - return directly to customer
            MotorControl::disable_motor();
            MotorControl::set_direction(Direction::LEFT);

            // Set slow speed for return movement
            MotorControl::setMotorSpeed(SLOW_SPEED);

            // Start return movement to customer
            MotorControl::stepEnabled = true;
            MotorControl::stepTimer->resume();
            cupState = CupState::RETURNING_TO_CUSTOMER;
            LEDControl::setLedColor(Color::RED);
            LEDControl::turnOffInsideLed();
            cupProcessStartTime = currentTime;
            finalStepCount = 0;

            DEBUG_PRINTLN("Cup at scanning point - returning directly to customer");
        }

        // NPN_INPUT_3 (reverse point) -> return cup to customer
        if (digitalRead(NPN_INPUT_3)) {
            // Cup at reverse point - return directly to customer
            MotorControl::disable_motor();
            MotorControl::set_direction(Direction::LEFT);

            // Set slow speed for return movement
            MotorControl::setMotorSpeed(SLOW_SPEED);

            // Start return movement to customer
            MotorControl::stepEnabled = true;
            MotorControl::stepTimer->resume();
            cupState = CupState::RETURNING_TO_CUSTOMER;
            LEDControl::setLedColor(Color::RED);
            LEDControl::turnOffInsideLed();
            cupProcessStartTime = currentTime;
            finalStepCount = 0;

            DEBUG_PRINTLN("Cup at reverse point - returning directly to customer");
        }

        // NPN_INPUT_4 (bin point) -> move cup to start point
        if (digitalRead(NPN_INPUT_4)) {
            // Prepare motor for return movement (rotate left)
            MotorControl::disable_motor();
            MotorControl::set_direction(Direction::LEFT);

            // Ensure normal speed for bin return
            MotorControl::setMotorSpeed(NORMAL_SPEED);

            // Start movement back to start
            MotorControl::stepEnabled = true;
            MotorControl::stepTimer->resume();
            cupState = CupState::MOVING_TO_START;
            cupProcessStartTime = currentTime;
            finalStepCount = 0;
        }
    }

    // Monitor end-switches for error conditions
    // NPN_INPUT_5 (start point end-switch) - error if triggered when not WAITING_FOR_CUP
    if (digitalRead(NPN_INPUT_5) && cupState != CupState::WAITING_FOR_CUP) {
        MotorControl::stepEnabled = false;
        MotorControl::stepTimer->pause();
        digitalWrite(STEP_PIN, LOW);
        MotorControl::enable_motor();

        Serial2.println("ERROR: Start point end-switch triggered unexpectedly");
        cupState = CupState::ERROR;
        DEBUG_PRINTLN("Entered ERROR state due to NPN_INPUT_5");
        LEDControl::setLedColor(Color::RED);
        LEDControl::turnOffInsideLed();
        return;
    }

    // NPN_INPUT_6 (bin end-switch) - error if triggered regardless of state
    if (digitalRead(NPN_INPUT_6)) {
        MotorControl::stepEnabled = false;
        MotorControl::stepTimer->pause();
        digitalWrite(STEP_PIN, LOW);
        MotorControl::enable_motor();

        Serial2.println("ERROR: Bin end-switch triggered");
        cupState = CupState::ERROR;
        DEBUG_PRINTLN("Entered ERROR state due to NPN_INPUT_6");
        LEDControl::setLedColor(Color::RED);
        LEDControl::turnOffInsideLed();
        return;
    }

    switch (cupState) {
        case CupState::WAITING_FOR_CUP:
            if (digitalRead(NPN_INPUT_1)) {     // if cup was inserted
                Serial2.println("Cup inserted, moving cup to scanning point");

                // Prepare motor for movement to scanning point (rotate right)
                MotorControl::disable_motor();        // disable motor for moving
                MotorControl::set_direction(Direction::RIGHT);

                // Initialize step counting variables
                npnInput1WasHigh = true;
                startStepCounting = false;
                errorStepCount = 0;

                // Start precise timer-based stepping
                MotorControl::stepEnabled = true;
                MotorControl::stepTimer->resume(); // Start the timer

                // turn on green led
                cupState = CupState::MOVING_TO_SCANNING_POINT;
                firstCupInserted = true;
                LEDControl::setLedColor(Color::GREEN);
                
                // turn on inside led
                LEDControl::turnOnInsideLed();
                
                cupDecisionReceived = false;
                cupProcessStartTime = currentTime;

                DEBUG_PRINTLN("Started precise timer-based movement to scanning point");
            }
            break;

        case CupState::MOVING_TO_SCANNING_POINT:
            // Check if NPN_INPUT_1 goes from 1 to 0 to start step counting
            if (npnInput1WasHigh && !digitalRead(NPN_INPUT_1)) {
                startStepCounting = true;
                errorStepCount = 0;
                DEBUG_PRINTLN("NPN_INPUT_1 turned off - starting step counting for error detection");
            }

            // Update NPN_INPUT_1 state tracking
            npnInput1WasHigh = digitalRead(NPN_INPUT_1);

            // Check if we've reached the scanning point
            if (digitalRead(NPN_INPUT_2)) {
                // Stop precise timer stepping
                MotorControl::stepEnabled = false;
                MotorControl::stepTimer->pause(); // Stop the timer
                digitalWrite(STEP_PIN, LOW); // Ensure step pin is low

                Serial2.println("Cup reached the scanning point");

                // Disable motor during scanning
                MotorControl::disable_motor();

                // turn on yellow led
                LEDControl::setLedColor(Color::YELLOW);

                cupState = CupState::SCANNING;
                cupProcessStartTime = currentTime;
                scanningStartTime = currentTime;  // Start scanning timeout timer

                DEBUG_PRINTLN("Entering scanning state - waiting for cup decision");
            }
            // Check for error condition: too many steps without reaching NPN_INPUT_2 OR timeout
            else if ((startStepCounting && errorStepCount > MAX_STEPS_ERROR && !digitalRead(NPN_INPUT_2)) ||
                     (currentTime - cupProcessStartTime >= MOVING_TO_SCANNING_POINT_TIMEOUT) ||
                     digitalRead(NPN_INPUT_3) || digitalRead(NPN_INPUT_4)) {
                // Stop motor and start error recovery
                MotorControl::stepEnabled = false;
                MotorControl::stepTimer->pause();
                digitalWrite(STEP_PIN, LOW);

                Serial2.println("ERROR: Too many steps without reaching scanning point - starting recovery");
                DEBUG_PRINTLN("Starting 2-second stop before error recovery");

                errorStopStartTime = currentTime;
                cupState = CupState::ERROR_RECOVERY;
                errorStepCount = 0; // Reset for recovery step counting
                LEDControl::turnOffInsideLed();
                // turn on red led
                LEDControl::setLedColor(Color::RED);
            }
            break;

        case CupState::SCANNING:
            // Check for scanning timeout (1 minute)
            if (currentTime - scanningStartTime >= SCANNING_TIMEOUT) {
                DEBUG_PRINTLN("SCANNING TIMEOUT - treating cup as NOT OK");
                Serial2.println("Scanning timeout - cup treated as NOT OK");

                // Treat as cup NOT OK - move to reverse point
                MotorControl::disable_motor();
                MotorControl::set_direction(Direction::RIGHT);

                // Start movement to reverse point
                MotorControl::stepEnabled = true;
                MotorControl::stepTimer->resume();
                cupState = CupState::MOVING_TO_REVERSE_POINT;
                LEDControl::setLedColor(Color::RED);
                LEDControl::turnOffInsideLed();
                cupProcessStartTime = currentTime;

                DEBUG_PRINTLN("Timeout: Moving to reverse point");
            }
            // Wait for cup decision command (CMD_CUP_OK or CMD_CUP_NOT_OK)
            else if (cupDecisionReceived) {
                if (cupOkDecision) {
                    DEBUG_PRINTLN("Cup OK - moving to bin");
                    // Prepare motor for movement to bin (rotate right)
                    MotorControl::disable_motor();
                    MotorControl::set_direction(Direction::RIGHT);

                    // Start movement to bin
                    MotorControl::stepEnabled = true;
                    MotorControl::stepTimer->resume();
                    cupState = CupState::MOVING_TO_BIN;
                    paymentProcessedReceived = false;

                    // turn on green led
                    LEDControl::setLedColor(Color::GREEN);

                    LEDControl::turnOffInsideLed();
                    cupProcessStartTime = currentTime;
                } else {
                    DEBUG_PRINTLN("Cup NOT OK - moving to reverse point");
                    // Prepare motor for movement to reverse point (rotate right)
                    MotorControl::disable_motor();
                    MotorControl::set_direction(Direction::RIGHT);

                    // Start movement to reverse point
                    MotorControl::stepEnabled = true;
                    MotorControl::stepTimer->resume();
                    cupState = CupState::MOVING_TO_REVERSE_POINT;
                    LEDControl::turnOffInsideLed();
                    cupProcessStartTime = currentTime;
                }

                // Reset decision flags
                cupDecisionReceived = false;
                cupOkDecision = false;
            }
            break;

        case CupState::MOVING_TO_BIN:
            // Check if we've reached the bin (NPN_INPUT_4)
            if (digitalRead(NPN_INPUT_4)) {
                // Stop movement
                MotorControl::stepEnabled = false;
                MotorControl::stepTimer->pause();
                digitalWrite(STEP_PIN, LOW);

                Serial2.println("Cup reached the bin");

                cupState = CupState::UNLOADING_AT_BIN;
                binWaitStartTime = currentTime;

                DEBUG_PRINTLN("Waiting at bin for 1 second");
            }
            // Check for timeout
            else if (currentTime - cupProcessStartTime >= MOVING_TO_BIN_TIMEOUT) {
                // Stop movement
                MotorControl::stepEnabled = false;
                MotorControl::stepTimer->pause();
                digitalWrite(STEP_PIN, LOW);

                Serial2.println("ERROR: Timeout while moving to bin - starting error recovery");
                DEBUG_PRINTLN("Starting error recovery due to bin movement timeout");

                errorStopStartTime = currentTime;
                cupState = CupState::ERROR_RECOVERY;
                errorStepCount = 0; // Reset for recovery step counting
                LEDControl::turnOffInsideLed();
                LEDControl::setLedColor(Color::RED);
            }
            break;

        case CupState::UNLOADING_AT_BIN:
            // Wait for 1 second at bin
            if (currentTime - binWaitStartTime >= BIN_WAIT_TIME) {
                DEBUG_PRINTLN("Bin wait complete - transitioning to WAITING_FOR_PAYMENT");

                // Transition to waiting for payment
                cupState = CupState::WAITING_FOR_PAYMENT;
                paymentWaitStartTime = currentTime;

                // Reset payment flag
                paymentProcessedReceived = false;

                DEBUG_PRINTLN("Waiting for payment processing command");
            }
            break;

        case CupState::WAITING_FOR_PAYMENT:
            // Wait for payment_processed command or timeout (60 seconds)
            // cupState = CupState::MOVING_TO_START;           // !!!!!!!!!!!!!!! DELETE THIS LINE !!!!!!!!!!!!!!!!!
            if (paymentProcessedReceived) {
                DEBUG_PRINTLN("Payment processed - starting return movement");

                // Prepare motor for return movement (rotate left)
                MotorControl::disable_motor();
                MotorControl::set_direction(Direction::LEFT);

                // Ensure normal speed for bin return
                MotorControl::setMotorSpeed(NORMAL_SPEED);

                // Start movement back to start
                MotorControl::stepEnabled = true;
                MotorControl::stepTimer->resume();
                cupState = CupState::MOVING_TO_START;
                cupProcessStartTime = currentTime;
                finalStepCount = 0;

                // Reset payment flag
                paymentProcessedReceived = false;

                DEBUG_PRINTLN("Moving attachment mechanism to start at normal speed");
            }
            // Check for timeout (60 seconds)
            else if (currentTime - paymentWaitStartTime >= PAYMENT_TIMEOUT) {
                DEBUG_PRINTLN("Payment timeout - starting return movement anyway");

                // Prepare motor for return movement (rotate left)
                MotorControl::disable_motor();
                MotorControl::set_direction(Direction::LEFT);

                // Ensure normal speed for bin return
                MotorControl::setMotorSpeed(NORMAL_SPEED);

                // Start movement back to start
                MotorControl::stepEnabled = true;
                MotorControl::stepTimer->resume();
                cupState = CupState::MOVING_TO_START;
                cupProcessStartTime = currentTime;
                finalStepCount = 0;

                DEBUG_PRINTLN("Moving attachment mechanism to start at normal speed after timeout");
            }
            break;

        case CupState::MOVING_TO_START:
            {
                static bool reachedStart = false;
                static bool startedFinalSteps = false;

                // Check if NPN_INPUT_1 turns to 1 (reached start area)
                if (digitalRead(NPN_INPUT_1) && !reachedStart) {
                    reachedStart = true;
                    DEBUG_PRINTLN("Reached start area - motor continues spinning");
                }

                // Check if NPN_INPUT_1 turns to 0 after being 1
                if (reachedStart && !digitalRead(NPN_INPUT_1) && !startedFinalSteps) {
                    startedFinalSteps = true;
                    finalStepCount = 0;
                    DEBUG_PRINTLN("NPN_INPUT_1 turned off - starting final 200 steps");
                }

                // Check if final steps are complete
                if (startedFinalSteps && finalStepCount >= FINAL_STEPS) {
                    // Stop motor after 200 steps
                    MotorControl::stepEnabled = false;
                    MotorControl::stepTimer->pause();
                    digitalWrite(STEP_PIN, LOW);

                    // Reset to normal speed for next cycle
                    MotorControl::setMotorSpeed(NORMAL_SPEED);
                    MotorControl::enable_motor();

                    Serial2.println("Cup process complete - returning to waiting");
                    cupState = CupState::WAITING_FOR_CUP;

                    // Reset flashing LED variables for waiting state
                    LEDControl::resetWaitingLedState();

                    // Reset static variables for next cycle
                    reachedStart = false;
                    startedFinalSteps = false;
                    finalStepCount = 0;

                    // Reset payment variables
                    paymentProcessedReceived = false;
                    paymentWaitStartTime = 0;

                    DEBUG_PRINTLN("Final steps complete - ready for next cup");
                }

                // Check for timeout
                if (currentTime - cupProcessStartTime >= MOVING_TO_START_TIMEOUT) {
                    // Stop movement
                    MotorControl::stepEnabled = false;
                    MotorControl::stepTimer->pause();
                    digitalWrite(STEP_PIN, LOW);

                    Serial2.println("ERROR: Timeout while moving to start - starting error recovery");
                    DEBUG_PRINTLN("Starting error recovery due to start movement timeout");

                    errorStopStartTime = currentTime;
                    cupState = CupState::ERROR_RECOVERY;
                    errorStepCount = 0; // Reset for recovery step counting
                    LEDControl::setLedColor(Color::RED);

                    // Reset static variables
                    reachedStart = false;
                    startedFinalSteps = false;
                    finalStepCount = 0;
                }
            }

            break;

        case CupState::MOVING_TO_REVERSE_POINT:
            // Check if we've reached the reverse point (NPN_INPUT_3)
            if (digitalRead(NPN_INPUT_3)) {
                // Stop movement
                MotorControl::stepEnabled = false;
                MotorControl::stepTimer->pause();
                digitalWrite(STEP_PIN, LOW);

                Serial2.println("Cup reached the reverse point");

                // Reverse direction and start return movement with slow speed
                DEBUG_PRINTLN("Reversing direction - starting slow return to customer");

                // Prepare motor for return movement (rotate left)
                MotorControl::disable_motor();
                MotorControl::set_direction(Direction::LEFT);

                // Set slow speed for return movement
                MotorControl::setMotorSpeed(SLOW_SPEED);

                // Start return movement to customer
                MotorControl::stepEnabled = true;
                MotorControl::stepTimer->resume();
                cupState = CupState::RETURNING_TO_CUSTOMER;
                LEDControl::setLedColor(Color::RED);
                cupProcessStartTime = currentTime;
                finalStepCount = 0;

                // turn on red led

                DEBUG_PRINTLN("Returning cup to customer at slow speed");
            }
            // Check for timeout
            else if (currentTime - cupProcessStartTime >= MOVING_TO_REVERSE_POINT_TIMEOUT) {
                // Stop movement
                MotorControl::stepEnabled = false;
                MotorControl::stepTimer->pause();
                digitalWrite(STEP_PIN, LOW);

                Serial2.println("ERROR: Timeout while moving to reverse point - starting error recovery");
                DEBUG_PRINTLN("Starting error recovery due to reverse point movement timeout");

                errorStopStartTime = currentTime;
                cupState = CupState::ERROR_RECOVERY;
                errorStepCount = 0; // Reset for recovery step counting
                LEDControl::turnOffInsideLed();
                LEDControl::setLedColor(Color::RED);
            }
            break;

        case CupState::ERROR_RECOVERY:
            // Wait 2 seconds before starting recovery movement
            if (currentTime - errorStopStartTime >= ERROR_STOP_TIME) {
                // Start moving back to start point
                if (!MotorControl::stepEnabled) {
                    DEBUG_PRINTLN("Starting error recovery movement to start point");

                    MotorControl::disable_motor();
                    MotorControl::set_direction(Direction::LEFT); // Move left to return to start

                    MotorControl::stepEnabled = true;
                    MotorControl::stepTimer->resume();

                    DEBUG_PRINTLN("Error recovery: moving to start point");
                }
            }

            // Check if NPN_INPUT_1 turns to 1 during recovery - transition to normal MOVING_TO_START
            if (MotorControl::stepEnabled && digitalRead(NPN_INPUT_1)) {
                DEBUG_PRINTLN("NPN_INPUT_1 detected during error recovery - transitioning to MOVING_TO_START");

                // Transition to normal MOVING_TO_START state
                cupState = CupState::MOVING_TO_START;
                cupProcessStartTime = currentTime;
                finalStepCount = 0;

                Serial2.println("Error recovery successful - continuing with normal return sequence");
                DEBUG_PRINTLN("Now in MOVING_TO_START state - will complete 200 steps after NPN_INPUT_1 turns off");
            }
            // Check for second error condition during recovery: too many steps OR timeout (only if still in recovery)
            else if (MotorControl::stepEnabled &&
                     (errorStepCount > MAX_STEPS_ERROR ||
                      currentTime - errorStopStartTime >= ERROR_RECOVERY_TIMEOUT)) {
                // Second error - stop everything and enter error state
                MotorControl::stepEnabled = false;
                MotorControl::stepTimer->pause();
                digitalWrite(STEP_PIN, LOW);
                MotorControl::enable_motor(); // Enable motor but don't move

                if (errorStepCount > MAX_STEPS_ERROR) {
                    Serial2.println("CRITICAL ERROR: Recovery failed - too many steps - entering error state");
                } else {
                    Serial2.println("CRITICAL ERROR: Recovery failed - timeout - entering error state");
                }
                cupState = CupState::ERROR;

                DEBUG_PRINTLN("Entered ERROR state - motor enabled but stopped");
            }
            break;

        case CupState::ERROR:
            // Error state - motor is enabled but not moving
            // System stays in this state until manual intervention
            // Motor remains enabled as requested

            // Check for ERROR_PROCESSED command
            if (errorProcessedReceived) {
                DEBUG_PRINTLN("ERROR_PROCESSED command received - transitioning to WAITING_FOR_CUP");
                cupState = CupState::WAITING_FOR_CUP;

                // Reset flashing LED variables for waiting state
                LEDControl::resetWaitingLedState();

                LEDControl::turnOffInsideLed();
                errorProcessedReceived = false; // Reset the flag

                // Reset all cup process variables
                cupDecisionReceived = false;
                cupOkDecision = false;
                paymentProcessedReceived = false;
                useSlowSpeed = false;
                npnInput1WasHigh = false;
                startStepCounting = false;
                errorStepCount = 0;
                finalStepCount = 0;
                scanningStartTime = 0;
                paymentWaitStartTime = 0;

                Serial2.println("Error processed - system ready for new cup");
            }
            break;

        case CupState::RETURNING_TO_CUSTOMER:
            {
                static bool reachedStart = false;
                static bool startedFinalSteps = false;

                // Check if NPN_INPUT_1 turns to 1 (reached start area)
                if (digitalRead(NPN_INPUT_1) && !reachedStart) {
                    reachedStart = true;
                    DEBUG_PRINTLN("Reached start area during return - motor continues spinning");
                }

                // Check if NPN_INPUT_1 turns to 0 after being 1
                if (reachedStart && !digitalRead(NPN_INPUT_1) && !startedFinalSteps) {
                    startedFinalSteps = true;
                    finalStepCount = 0;
                    DEBUG_PRINTLN("NPN_INPUT_1 turned off during return - starting final 200 steps");
                }

                // Check if final steps are complete
                if (startedFinalSteps && finalStepCount >= FINAL_STEPS) {
                    // Stop motor after 200 steps
                    MotorControl::stepEnabled = false;
                    MotorControl::stepTimer->pause();
                    digitalWrite(STEP_PIN, LOW);

                    // Reset to normal speed for next cycle
                    MotorControl::setMotorSpeed(NORMAL_SPEED);
                    MotorControl::enable_motor();

                    Serial2.println("Cup returned to customer - process complete");
                    cupState = CupState::WAITING_FOR_CUP;

                    // Reset flashing LED variables for waiting state
                    LEDControl::resetWaitingLedState();

                    // Reset static variables for next cycle
                    reachedStart = false;
                    startedFinalSteps = false;
                    finalStepCount = 0;

                    // Reset payment variables
                    paymentProcessedReceived = false;
                    paymentWaitStartTime = 0;

                    DEBUG_PRINTLN("Cup return complete - ready for next cup");
                }

                // Check for timeout
                if (currentTime - cupProcessStartTime >= RETURNING_TO_CUSTOMER_TIMEOUT) {
                    // Stop movement
                    MotorControl::stepEnabled = false;
                    MotorControl::stepTimer->pause();
                    digitalWrite(STEP_PIN, LOW);

                    Serial2.println("ERROR: Timeout while returning to customer - starting error recovery");
                    DEBUG_PRINTLN("Starting error recovery due to customer return timeout");

                    errorStopStartTime = currentTime;
                    cupState = CupState::ERROR_RECOVERY;
                    errorStepCount = 0; // Reset for recovery step counting
                    LEDControl::setLedColor(Color::RED);

                    // Reset static variables
                    reachedStart = false;
                    startedFinalSteps = false;
                    finalStepCount = 0;
                }
            }
            break;
    }
}

} // namespace CupBoard
