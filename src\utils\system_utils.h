#ifndef SYSTEM_UTILS_H
#define SYSTEM_UTILS_H

#include <Arduino.h>
#include "../config/global_variables.h"
#include "../config/pinout.h"
#include "../communication/protocol.h"
#include "../types/cup_types.h"

/**
 * @brief Send cup state and door status information to Python script via Serial1
 * 
 * This function creates a packet containing:
 * - Cup state code (first data byte)
 * - Door status byte (second data byte, if security is enabled)
 * - Additional reserved bytes
 * 
 * The packet follows the standard protocol format with START_BYTE, command 0xFE,
 * length, payload, CRC16, and END_BYTE.
 */
void sendDataToPython();

/**
 * @brief Read door sensor states and update global door status byte
 *
 * This function reads the service door and bin door sensors and encodes
 * their status into the global doorStatusByte variable. Only processes
 * door status if SECURITY_STATE is enabled.
 *
 * Door status encoding:
 * - Bit 4: Service door (1=open, 0=closed)
 * - Bit 0: Bin door (1=open, 0=closed)
 *
 * Examples:
 * - 0x11 = both doors open
 * - 0x00 = both doors closed
 * - 0x01 = service closed, bin open
 * - 0x10 = service open, bin closed
 */
void doors();

/**
 * @brief Setup EEPROM based on startup configuration
 *
 * This function handles EEPROM initialization based on the eepromStartup setting:
 * - REWRITE: Write all global variables to EEPROM with their current values
 * - LOAD_FROM_IT: Load all global variables from EEPROM
 * - DO_NOTHING: Skip EEPROM operations
 *
 * Variables handled include timing parameters, LED settings, motor speeds,
 * and other configuration values that need to persist across reboots.
 */
void setupEEPROM();




// TODO: add definition
void jumpToBootloader();

#endif // SYSTEM_UTILS_H
